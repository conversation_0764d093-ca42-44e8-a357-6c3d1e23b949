# 小美人际关系管理系统 API 文档

## 概述

小美人际关系管理系统提供了一套完整的API接口，用于管理人际关系、记忆存储和智能对话功能。系统基于FastAPI框架构建，支持流式和非流式响应。

**测试环境地址**: `http://xiaomeiai.cloud.test.sankuai.com`
**生产环境地址**: `https://xiaomeiai.meituan.com/humanrelation`

## 目录

1. [记忆管理接口](#记忆管理接口)
   - 搜索记忆
   - 添加记忆
   - 读取记忆
   - 更新长期记忆
   - 检查过期信息
   - 根据人员ID搜索记忆
   - 获取用户所有短期记忆
   - 获取用户所有人员ID
2. [对话聊天接口](#对话聊天接口)
3. [人员管理接口](#人员管理接口)
4. [会话管理接口](#会话管理接口)
5. [提醒管理接口](#提醒管理接口)
6. [系统监控接口](#系统监控接口)

---

## 记忆管理接口

### 1. 搜索记忆

**接口**: `POST /humanrelation/search_memory`

**描述**: 根据语义相关性搜索用户的记忆内容

**请求参数**:
```json
{
  "user_input": "string",     // 用户输入内容，根据语义相关性查询
  "size": "integer",          // 返回结果数量
  "k": "integer",             // 候选数量
  "user_id": "string",        // 查询者的用户 mis id
  "memory_type": "string"     // 记忆类型，分long or short
}
```

**响应示例**:
```json
{
  "results": [
    {
      "content": "记忆内容",
      "score": 0.95,
      "timestamp": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 2. 添加记忆

**接口**: `POST /humanrelation/add_memory`

**描述**: 从对话文本中提取并存储人员和事件记忆

**请求参数**:
```json
{
  "conversation_text": "string",  // 对话文本
  "user_id": "string"             // 用户ID
}
```

**响应示例**:
```json
{
  "status": "success",
  "message": "记忆添加成功",
  "extracted_entities": ["人员1", "事件1"]
}
```

### 3. 读取记忆

**接口**: `POST /humanrelation/read_memory`

**描述**: 根据查询文本检索相关的人员和事件记忆

**请求参数**:
```json
{
  "query_text": "string",     // 查询文本
  "user_id": "string",        // 用户ID
  "max_results": "integer"    // 最大结果数，默认10
}
```

### 4. 更新长期记忆

**接口**: `POST /humanrelation/update_long_memory`

**描述**: 手动触发长期记忆更新，将短期记忆总结为人物小传

**请求参数**:
- `user_id` (query parameter): 用户ID

### 5. 检查过期信息

**接口**: `GET /humanrelation/check_outdated`

**描述**: 检查需要更新的过期状态信息

**请求参数**:
- `user_id` (query parameter): 用户ID
- `days` (query parameter): 过期天数，默认180天

### 6. 根据人员ID搜索记忆

**接口**: `POST /humanrelation/search_memory_by_person_id`

**描述**: 根据人员ID搜索相关的事件记忆

**请求参数**:
```json
{
  "user_id": "string",      // 用户ID
  "person_id": "string"     // 人员ID
}
```

**响应示例**:
```json
{
  "result": "success",
  "events": [
    {
      "event_id": "uuid-here",
      "user_id": "user123",
      "description_text": "和张三讨论项目进展",
      "timestamp": "2024-01-01T10:00:00Z",
      "participants": ["person_id_123"],
      "location": "会议室A",
      "topics": ["项目", "进展"],
      "sentiment": "积极"
    }
  ]
}
```

### 7. 获取用户所有短期记忆

**接口**: `GET /humanrelation/get_all_short_memory_by_user_id`

**描述**: 根据用户ID获取对应的所有短期记忆（事件记忆）

**请求参数**:
- `user_id` (query parameter): 用户ID
- `size` (query parameter): 返回数量，默认100

**响应示例**:
```json
{
  "result": "success",
  "events": [
    {
      "event_id": "uuid-here",
      "user_id": "user123",
      "description_text": "和李四在咖啡厅讨论新项目",
      "timestamp": "2024-01-01T15:30:00Z",
      "participants": ["person_id_456"],
      "location": "星巴克",
      "topics": ["项目", "讨论"],
      "sentiment": "积极"
    }
  ]
}
```

### 8. 获取用户所有人员ID

**接口**: `GET /humanrelation/get_all_person_id_by_user_id`

**描述**: 根据用户ID获取所有相关人员的ID列表

**请求参数**:
- `user_id` (query parameter): 用户ID

**响应示例**:
```json
{
  "result": "success",
  "person_ids": [
    "person_id_123",
    "person_id_456",
    "person_id_789"
  ],
  "count": 3
}
```

---

## 对话聊天接口

### 1. 流式聊天

**接口**: `POST /humanrelation/chat`

**描述**: 流式聊天接口，使用 conversation_id 维护上下文

**请求参数**:
```json
{
  "content": "string",           // 用户问题
  "conversation_id": "string",   // 对话 ID，用于上下文关联
  "user_id": "string"           // 用户ID
}
```

**响应**: Server-Sent Events (SSE) 流式响应
```
Content-Type: text/event-stream

data: {"type": "message", "content": "回复内容片段1"}
data: {"type": "message", "content": "回复内容片段2"}
data: {"type": "end"}
```

### 2. JSON格式聊天

**接口**: `POST /humanrelation/chat_json`

**描述**: 非流式聊天接口，返回JSON格式

**请求参数**:
```json
{
  "content": "string",           // 用户问题
  "conversation_id": "string",   // 对话 ID，用于上下文关联
  "user_id": "string"           // 用户ID
}
```

**响应示例**:
```json
{
  "response": "AI回复内容",
  "status": "success"
}
```

---

## 人员管理接口

### 1. 获取所有人员列表

**接口**: `GET /humanrelation/persons`

**描述**: 获取所有人员列表

**请求参数**:
- `user_id` (query parameter): 用户ID
- `limit` (query parameter): 返回数量，默认100
- `offset` (query parameter): 偏移量，默认0

**响应示例**:
```json
{
  "result": "success",
  "persons": [
    {
      "person_id": "uuid-here",
      "user_id": "user123",
      "canonical_name": "张三",
      "aliases": "小张",
      "relationships": ["同事"],
      "profile_summary": "公司同事",
      "key_attributes": {
        "关系": "同事",
        "年龄": "30",
        "职业": "工程师"
      },
      "avatar": "",
      "is_user": false
    }
  ]
}
```

### 2. 获取单个人员信息

**接口**: `GET /humanrelation/person/{person_id}`

**描述**: 根据ID获取人员详细信息

**请求参数**:
- `user_id` (query parameter): 用户ID
- `person_id` (path parameter): 人员ID

**响应示例**:
```json
{
  "result": "success",
  "person": {
    "person_id": "uuid-here",
    "user_id": "user123",
    "canonical_name": "张三",
    "aliases": "小张",
    "relationships": ["同事"],
    "profile_summary": "公司同事",
    "key_attributes": {
      "关系": "同事",
      "年龄": "30",
      "职业": "工程师"
    },
    "avatar": "",
    "is_user": false
  }
}
```

### 3. 添加人员

**接口**: `POST /humanrelation/add_person`

**描述**: 添加新人员

**请求参数**:
```json
{
  "user_id": "string",           // 用户ID
  "canonical_name": "string",    // 正式姓名
  "aliases": "string",           // 别名，默认空字符串
  "relationships": [],           // 人际关系，默认空数组
  "profile_summary": "string",   // 个人简介，默认空字符串
  "key_attributes": {},          // 关键属性，默认空对象
  "avatar": "string",            // 头像URL，默认空字符串
  "is_user": false              // 是否为用户，默认false
}
```

**注意**: 系统会自动生成 `person_id`，无需在请求中提供。如果需要指定特定的 `person_id`，可以通过后端服务层的 `person_id` 参数传入。

**响应示例**:
```json
{
  "result": "success",
  "person_id": "generated-uuid-here"
}
```

### 4. 更新人员信息

**接口**: `PUT /humanrelation/change_person/{person_id}`

**描述**: 更新人员档案信息

**请求参数**:
- `person_id` (path parameter): 人员ID
- 请求体同添加人员接口

**响应示例**:
```json
{
  "result": "success"
}
```

### 5. 删除人员

**接口**: `DELETE /humanrelation/person/{person_id}`

**描述**: 删除指定人员

**请求参数**:
- `user_id` (query parameter): 用户ID
- `person_id` (path parameter): 人员ID

**响应示例**:
```json
{
  "result": "success"
}
```

### 6. 搜索人员

**接口**: `GET /humanrelation/search_person`

**描述**: 按姓名搜索人员

**请求参数**:
- `user_id` (query parameter): 用户ID
- `name` (query parameter): 搜索的姓名
- `limit` (query parameter): 结果限制数量，默认10

**响应示例**:
```json
{
  "result": "success",
  "persons": [
    {
      "person_id": "uuid-here",
      "canonical_name": "张三",
      "aliases": "小张",
      "relationships": ["同事"],
      "profile_summary": "公司同事",
      "key_attributes": {
        "关系": "同事",
        "年龄": "30",
        "职业": "工程师"
      },
      "avatar": "",
      "is_user": false
    }
  ]
}
```

### 7. 合并人员档案

**接口**: `POST /humanrelation/merge_persons`

**描述**: 确认并执行两个人员档案的合并

**请求参数**:
```json
{
  "user_id": "string",              // 用户ID
  "primary_person_id": "string",    // 主要人员ID
  "secondary_person_id": "string"   // 次要人员ID
}
```

**响应示例**:
```json
{
  "result": "success",
  "message": "人员档案合并成功"
}
```

---

## 会话管理接口

### 1. 生成新的会话ID

**接口**: `POST /humanrelation/create_conversation`

**描述**: 为新对话生成一个唯一的、带时间戳的 conversation_id

**请求参数**:
```json
{
  "user_id": "string"    // 用户ID
}
```

**响应示例**:
```json
{
  "success": true,
  "conversation_id": "user123_2024-01-01-10-30-45"
}
```

### 2. 获取用户的所有会话ID

**接口**: `GET /humanrelation/conversations`

**描述**: 根据用户ID获取其所有历史对话的 conversation_id 列表

**请求参数**:
- `user_id` (query parameter): 用户ID

**响应示例**:
```json
{
  "success": true,
  "user_id": "user123",
  "conversation_ids": [
    "user123_2024-01-01-10-30-45",
    "user123_2024-01-02-14-20-30"
  ]
}
```

### 3. 获取聊天记录

**接口**: `GET /humanrelation/history`

**描述**: 根据对话ID获取完整的聊天记录

**请求参数**:
- `conversation_id` (query parameter): 对话ID
- `user_id` (query parameter): 用户ID

**响应示例**:
```json
{
  "conversation_id": "conv_123",
  "history": [...],
  "status": "success"
}
```

---

## 提醒管理接口

### 1. 添加提醒

**接口**: `POST /humanrelation/add_reminder`

**描述**: 添加一条提醒到系统中

**请求参数**:
```json
{
  "user_id": "string",                    // 用户ID
  "base_event_date": "2024-01-01T00:00:00Z",  // 基础事件日期
  "next_trigger_time": "2024-01-01T00:00:00Z", // 下次触发时间
  "subject_person_id": "string",          // 相关人员ID（可选）
  "reminder_text_template": "string",     // 提醒文本模板（可选）
  "advance_notice_config": {},            // 提前通知配置（可选）
  "recurrence_rule": "string",            // 重复规则（可选）
  "status": "active"                      // 状态，默认为active
}
```

**响应示例**:
```json
{
  "success": true,
  "reminder_id": "12345"
}
```

### 2. 检查提醒线程状态

**接口**: `GET /humanrelation/reminder_thread_status`

**描述**: 监察接口，检查后台提醒线程状态

**响应示例**:
```json
{
  "is_alive": true,
  "start_time": "2024-01-01 10:00:00",
  "status_message": "线程正常运行"
}
```

### 3. 重启提醒线程

**接口**: `POST /humanrelation/restart_reminder_thread`

**描述**: 重启后台提醒线程

**响应示例**:
```json
{
  "success": true,
  "message": "后台提醒线程重启成功"
}
```

---

## 系统监控接口

### 1. 健康检查

**接口**: `GET /monitor/alive`

**描述**: 健康检查接口，用于部署时的存活性检查

**响应示例**:
```json
{
  "status": "ok",
  "message": "Service is alive"
}
```



---

## 错误响应格式

所有接口在发生错误时都会返回统一的错误格式：

```json
{
  "status": "error",
  "message": "错误描述信息",
  "error_code": "ERROR_CODE"
}
```

## 认证说明

所有接口都需要提供 `user_id` 参数进行用户身份识别。系统使用用户ID来隔离不同用户的数据。

## 注意事项

1. 所有POST请求的Content-Type应为 `application/json`
2. 流式聊天接口返回的是Server-Sent Events格式
3. 人员管理相关接口支持MySQL数据库存储
4. 记忆管理使用ElasticSearch进行语义搜索
5. 系统支持长期和短期记忆的分类管理
6. 提醒系统支持后台线程自动处理，每10分钟检查一次待处理提醒
7. 聊天记录通过MySQL检查点保存器进行持久化存储
8. 短期记忆（事件记忆）存储在ElasticSearch中，通过timestamp字段按时间排序
9. 获取人员ID列表接口默认限制返回1000条记录，避免数据量过大
10. 所有记忆相关接口都支持用户数据隔离，确保数据安全性
