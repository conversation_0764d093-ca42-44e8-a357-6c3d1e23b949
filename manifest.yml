build:
  tools:
    build-private-key:
    devtoolset-9: ""
    python_with_virtualenv_and_buildout: 3.13 #3.10,注意这里和下面的python版本必须要保持一致
  run:
    workDir: ./
    cmd:
      - source /opt/rh/devtoolset-9/enable
      - sh build.sh
  target:
    distDir: ./
    files:
      - ./

autodeploy:
  hulkos: centos7
  tools:
    python_with_virtualenv_and_buildout: 3.13 #3.10,注意这里和上面的python版本必须要保持一致
  targetDir: /opt/meituan/xiaomei-humanrelation
  run: sh install.sh
  check: sh check.sh `hostname`:8080/monitor/alive
  checkRetry: 10
  checkInterval: 20s