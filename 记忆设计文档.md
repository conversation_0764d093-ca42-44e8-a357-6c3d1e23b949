人际关系助手记忆设计

一、 核心设计思想：从“事件为中心”到“人物为中心”

我们摒弃了通用化的记忆模型，采用更适合人际关系管理的 “以人物为中心” (Entity-Centric) 模型。系统中的核心不再是零散的记忆片段，而是具体的一个个“人”。所有的事件、信息和关系，都将围绕这些“人物”实体来组织和构建。

这种模式能更好地解决您的核心需求：

身份识别与消歧： 清晰地管理每个人的多个称呼（“王总”、“王叔叔”、“<PERSON> Wang”）。

关系网络构建： 明确知道“A是B的妻子”，“A和C是同事”，形成一个以用户为中心的关系图谱。

精准回忆与建议： 当您要见某人时，系统可以迅速调取关于这个“人”的立体化档案，而不仅仅是零散事件。

核心概念的演进与统一

短期记忆 -> 事件记忆库 (Event Store): 您原方案中的“短期记忆”被完整继承，并升级为“事件记忆库”。它依然是按时间顺序记录所有具体交互的流水日志，是所有分析的基础。

长期记忆 -> 人物小传 (Profile Summary): “长期记忆”不再是零散的要点，而是进化为与每个人物绑定的“人物小传”。系统会定期分析与某人相关的所有短期事件，提炼总结成一段连贯的人物描述，实现了长期记忆的精准锚定。

用户画像 -> 用户个人档案: 我们将用户自己也视为“人物档案库”中的一个特殊核心成员，从而将用户画像无缝融入整体架构，使用户成为所有关系的中心。

二、 数据架构

1. 核心数据库：人物档案库 (Person Store)

这是整个系统的大脑和基石，建议使用结构化数据库（如 Firestore, PostgreSQL, 或 MySQL）存储。

每个“人物”（包括用户自己）对应一条记录，包含以下字段：

person_id: 唯一ID (例如 UUID)。

is_user: 布尔值，用于标识此条记录是否为用户本人。

canonical_name: 主要称呼/真实姓名 (例如 "王建国")。

aliases: 别名/称呼数组 (例如 ["老王", "王总", "建国叔"])。

relationships: 关系数组 (例如 [{type: "同事", target_person_id: "..."}, {type: "配偶", target_person_id: "..."}])。

profile_summary: 人物小传 (升级版长期记忆)。由 LLM 根据所有相关事件动态生成和更新的一段描述性文字，如“王建国，我的大学同学，现就职于XX公司，热爱钓鱼，女儿正在上高中，最近在关注日本旅游攻略。”

key_attributes: 关键信息 (JSON/Map)。用于存储结构化信息，如 {"生日": "1980-05-10", "公司": "ABC科技", "职位": "销售总监"}。

avatar: 头像链接。

2. 事件记忆库 (Event Store)

这部分沿用 VDB 方案，存储所有具体的记忆事件，并通过元数据与“人物档案库”关联。

每个向量代表一个“记忆事件”，其元数据应包含：

vector: 事件描述文本的向量。

metadata:

event_id: 事件唯一ID。

description_text: 事件的文字描述 (例如 "和李雪一起吃了午饭，聊了她孩子升学的事情")。

timestamp: 事件发生的时间。

participants: 参与者数组 (存储的是 person_id 的列表)。

location: 地点。

topics: 话题标签数组 (由LLM提取)。

sentiment: 情感色彩 (例如 积极, 消极, 中性)。

三、 核心工作流程

步骤一：输入处理与实体识别 (Librarian)

用户输入:

“昨天下午和公司新来的同事张伟聊了聊，他好像是我大学师兄李涛的朋友。对了，他孩子5岁了。”

LLM 结构化提取:
从文本中提取出人物、关系和事件。

提及人物: ["张伟", "李涛"]

关系: 张伟是同事，张伟是李涛的朋友。

事件: 和张伟聊天，得知他孩子5岁了。

时效性信息处理 (计算+验证):

系统识别出“5岁”是时效信息，并知道当前是2025年。

后台计算: 2025 - 5 = 2020。

以验证口吻回应: “好的，记下了。这么算来，张伟孩子的出生年份应该是 2020年 左右，对吗？如果能告诉我月份，以后提醒生日就更准了！”

人物身份确认 (Entity Resolution):
系统遍历每个人物，在“人物档案库”中进行匹配。

"张伟": 未找到 -> 提问：“这是您第一次提到‘张伟’，需要我为他创建一份新档案吗？” -> 用户确认后创建新记录，并将计算出的孩子出生年份存入其 key_attributes。

"李涛": 找到匹配 -> 获取 person_id。

数据入库:

更新或创建人物信息到“人物档案库”。

将“聊天”事件关联上对应人物的 person_id 后，存入“事件记忆库”。

步骤二：回忆与建议生成 (Advisor)

当用户需要回忆或获得建议时，系统将从两个数据库中提取信息并进行智能整合。

用户提问:

“我马上要和张伟开会了，有什么可以聊的吗？”

数据检索:

人物档案检索: 获取“张伟”的完整档案，包括他的人物小传、关键信息（系统会自动计算出孩子当前年龄）和关系。

事件记忆检索: 在VDB中搜索所有与“张伟”相关的记忆事件。

LLM 智能整合与生成:
系统将检索到的所有信息构建成一个丰富的 Prompt，交给 LLM 进行最终的文本生成。

背景信息: “你是一个人际关系助手，现在用户即将与他的同事张伟开会，需要一些建议。”

人物档案: 姓名: 张伟, 关系: 同事, 关键信息: 孩子约5岁（2020年生）, 人物小传: ...是李涛的朋友...

相关记忆: ...

生成任务: “请根据以上信息，为用户提供2-3个可以在会前或会后与张伟聊的轻松话题。”

输出结果:
LLM 会生成高度情景化的建议，例如可以聊聊共同的熟人李涛，或者关心一下他5岁的孩子最近有什么趣事。

四、 核心机制详解：让记忆“活起来”

为了防止信息随着时间流逝而变得陈旧，系统必须具备一套智能更新机制。我们采用两种核心策略来处理不同类型的时效性信息。

策略一：针对可计算信息（如年龄），存储“基准事实”并动态计算

系统遵循的核心原则是：存储“不变的核心事实”，而不是“可变的表面信息”。

智能识别与推算： 当用户输入“张伟的孩子5岁了”时，系统不会直接存储“5岁”。LLM会识别出这是一个随时间变化的值，并根据当前年份（例如2025年）立刻在后台计算出“出生年份”这个不变的基准事实（2025 - 5 = 2020）。

计算+验证式交互： 系统不会默默记下推算结果，而是通过巧妙的验证式回应来确认并争取更精确的信息，这极大地提升了用户体验。如上文工作流程所示，系统会回应：“好的，记下了。这么算来，他/她的出生年份应该是 2020年 左右，对吗？如果能告诉我月份，以后提醒生日就更准了！”

动态计算与呈现： 系统在 key_attributes 中存储的是 { "孩子生日": "2020" }。未来当用户查询时，系统会实时地用当前年份减去存储的出生年份，永远提供最准确的年龄。

策略二：针对不可计算的状态（如职位、年级），采用“时间戳+定期回顾”

有些信息无法通过简单计算来更新，例如“职位”、“公司”、“孩子上小学了”。

作为带时间戳的事件记录： 当用户说“李雪现在是市场部经理了”，系统首先会在“事件记忆库”里记录一条带时间戳的事件。同时，也会在李雪的 key_attributes 里更新：{"职位": "市场部经理", "职位更新时间": "2025-06-23"}。

设计“维护机器人”进行定期回顾： 我们可以设计一个后台任务，它会定期（比如每半年或一年）扫描所有人物档案。当它发现一些有时效性的状态信息（如职位、就学情况）在很久以前更新过，它就可以主动地、非打扰式地提醒用户。

系统提醒示例： “我记录的‘李雪’的职位是‘市场部经理’（信息录入于2年前），这个信息还需要更新吗？”

通过这两种策略的结合，我们就能确保系统中的信息尽可能地保持“新鲜”和准确，让它成为一个真正可靠的、与时俱进的记忆伙伴。

为啥要用结构化数据库?

数据结构清晰，保证质量： “人物”是我们系统最核心的数据。每个“人物”都应该有相同的“骨架”（比如姓名、别名、关系、生日等字段）。结构化数据库会强制所有记录都遵循这个统一的格式，不会出现有的记录缺字段、有的记录字段名写错的情况，从根本上保证了数据的干净和一致。

查询精准高效： 我们经常需要进行非常精确的查找，比如“找到所有在ABC公司工作的人”或者“找出叫王建国的人”。结构化数据库对这种基于特定字段值的精确查询（WHERE company = 'ABC科技'）做了深度优化，速度极快。而这正是VDB（向量数据库）不擅长的事情。

关系管理更可靠： 虽然我们暂时不用真正的图数据库，但在结构化数据库中通过 relationships 字段来管理关系，依然比在非结构化数据库中更可靠。我们可以清楚地定义关系，并在未来进行更复杂的查询（比如找出所有“同事”关系的人）。

数据更新安全： 当我们更新一个人的信息时，可能需要同时修改好几个地方（比如更新职位，同时更新“人物小传”）。结构化数据库的“事务”功能可以保证这些操作要么全部成功，要么全部失败，不会出现只改了一半的“脏数据”情况。

简单来说，就是**“选对的工具做对的事”**：

事件记忆库 (Event Store)：核心需求是“语义模糊搜索”（比如“找找最近关于项目的记忆”），所以用 VDB。

人物档案库 (Person Store)：核心需求是“信息准确、结构统一、按字段精确查找”，所以用结构化数据库。

那为什么不用ES？

虽然 Elasticsearch (ES) 非常强大，但对于存储“人物档案库”这种核心数据来说，它不是最合适的工具，就像我们不会用一把最好的锤子去拧螺丝。

原因如下：

核心职责不同：

Elasticsearch 的核心是“搜索引擎”。它最擅长的是对大量的文字进行快速的模糊搜索、全文检索和相关性排序。比如，你输入“红色跑车”，它能帮你找到所有提到“红色”或“跑车”的商品，并把最相关的排在前面。

结构化数据库（如MySQL, PostgreSQL）的核心是“数据管家”。它最擅长的是保证数据的准确性、一致性和完整性。它要求数据必须按照规定好的格式（“骨架”）存放，保证每一条都清晰、不错乱。

我们需要的是“精确”而非“相关”：
当我们在“人物档案库”里查找数据时，我们的需求是极其精确的。比如“找到ID为123的张伟”，我们不希望系统返回一个“可能有点像张伟”的列表，我们就要那一个准确无误的结果。这种精确查找是结构化数据库的看家本领。而ES的核心——相关性排序，在这里反而成了不必要的功能。

数据安全与一致性（事务）：
“人物档案”是我们系统的核心事实，绝对不能出错。比如，我们想同时“给张伟添加一个新别名”并“更新他的公司名称”，这两个操作必须同时成功。结构化数据库的**“事务”**功能就是为此而生，能确保数据更新的绝对安全。ES虽然也能更新，但在这方面的保障不如传统数据库来得稳固。

总结一下：

我们为系统里的两种不同数据，选择了两种最适合的工具：

人物档案库 (Person Store): 像我们每个人的“身份证档案”，信息必须绝对准确无误。所以我们用结构化数据库这个“数据管家”。

事件记忆库 (Event Store): 像我们脑海中的“模糊回忆”，需要根据一个念头“联想”出相关的事情。所以我们用**VDB（向量数据库）**这个“AI联想家”。

而 Elasticsearch 在这个架构里，它的核心功能（全文搜索）恰好被更现代的VDB在“语义联想”这个任务上替代了。所以，不是ES不好，而是在我们这个精心设计的方案里，有比它更适合“人物档案库”这个岗位的工具。

接口：

添加记忆

读取记忆

查看所有人列表

查看某个人（只能用mysql）

删除某个人

增加某个人

修改某个人的档案

搜索某个人

是否判断要记