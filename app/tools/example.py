from langchain_core.tools import tool
from pydantic import BaseModel, Field

class calculator_plus_input(BaseModel):
    """
    示例工具的示例输出.
    """
    num1: int = Field(description="第一个加数")
    num2: int = Field(description="第二个加数")

@tool(description="计算两个整数的和", args_schema=calculator_plus_input)
def calculator_plus(num1: int, num2: int) -> int:
    """
    计算两个数的和
    """
    if not isinstance(num1, int) or not isinstance(num2, int):
        return "输入的参数不是整数"
    return num1 + num2



