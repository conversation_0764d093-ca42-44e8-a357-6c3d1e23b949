
from langchain.agents import Tool
from typing import Callable, List, Optional, Union, Any

# 全局工具池，用于存储注册的工具
_global_tool_pool: List[Any] = []

def register_tool(name: Optional[str] = None, description: Optional[str] = None) -> Callable:
    """
    一个装饰器，用于将一个函数包装成 Langchain Tool，并将其注册到全局工具池中。

    参数:
        name (Optional[str]): 工具的名称。如果为 None，则使用被装饰函数的名称。
        description (Optional[str]): 工具的描述。如果为 None，则使用被装饰函数的文档字符串 (docstring)。
                                   这个描述对于 Agent 理解如何使用该工具至关重要。

    返回:
        Callable: 返回原始的被装饰函数，使其仍然可以被直接调用。

    异常:
        ValueError: 如果工具名称或描述最终为空。
    """
    def decorator(func: Callable) -> Callable:
        tool_name = name if name else func.__name__
        
        docstring = func.__doc__
        tool_description = description
        
        if tool_description is None and docstring:
            # 清理文档字符串以用作描述：去除首尾空格，并将多行合并为一行。
            tool_description = " ".join(docstring.strip().split())
        
        if not tool_name:
            raise ValueError("工具名称不能为空。请在装饰器中提供名称或确保函数有名称。")
        
        if not tool_description:
            raise ValueError(
                f"工具 '{tool_name}' 必须有一个描述。 "
                "请在装饰器中提供描述，或为函数编写文档字符串。"
            )

        new_tool = Tool(
            name=tool_name,
            func=func,
            description=tool_description
        )
        _global_tool_pool.append(new_tool)
        # print(f"工具 '{tool_name}' 已注册并添加到工具池。") # 可选: 用于调试
        return func  # 返回原始函数
    return decorator

def add_tools(tools: Union[Tool, List[Tool]] = None) -> List[Tool]:
    global _global_tool_pool
    if isinstance(tools, List):
        _global_tool_pool.extend(tools)
    else:
        _global_tool_pool.append(tools)
    return _global_tool_pool

def get_all_tools() -> List[Tool]:
    """
    返回全局工具池中所有已注册工具的列表副本。
    """
    return list(_global_tool_pool)

def clear_all_tools():
    """
    从全局工具池中移除所有工具。
    """
    _global_tool_pool.clear()
    # print("全局工具池已清空。") # 可选: 用于调试

def initialize_tools(tools: List[Tool]):
    """
    使用提供的工具列表初始化或替换全局工具池。

    参数:
        tools (List[Tool]): 用于初始化工具池的 Langchain Tool 对象列表。
    """
    global _global_tool_pool
    _global_tool_pool = list(tools) # 使用列表的副本
    # print(f"全局工具池已通过 {len(_global_tool_pool)} 个工具进行初始化。") # 可选: 用于调试

# 示例用法 (可以取消注释以进行测试):
# @register_tool(description="一个简单的问候工具。")
# def greet(name: str) -> str:
#     """向指定的人打招呼。"""
#     return f"你好, {name}!"

# @register_tool()
# def farewell(name: str) -> str:
#     """向指定的人告别。"""
#     return f"再见, {name}!"

# if __name__ == "__main__":
#     # 注册工具后，可以获取它们
#     all_my_tools = get_all_tools()
#     print(f"当前注册的工具有: {len(all_my_tools)}")
#     for t in all_my_tools:
#         print(f" - 名称: {t.name}, 描述: {t.description}")
#         if t.name == "greet":
#             print(f"   调用 greet('世界'): {t.func('世界')}")

#     # 可以使用 initialize_tools 覆盖工具池
#     new_tool_list = [Tool(name="ping", func=lambda: "pong", description="返回pong")]
#     initialize_tools(new_tool_list)
#     all_my_tools = get_all_tools()
#     print(f"\n用 initialize_tools 更新后的工具数量: {len(all_my_tools)}")
#     for t in all_my_tools:
#         print(f" - 名称: {t.name}, 描述: {t.description}")

#     # 清空工具池
#     clear_all_tools()
#     print(f"\n清空工具池后的工具数量: {len(get_all_tools())}")
