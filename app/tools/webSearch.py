from typing import Dict, List, Literal, Optional, Tuple, Any
import sys
import random
import os

# 动态获取app目录路径，支持不同环境
current_dir = os.path.dirname(os.path.abspath(__file__))  # tools目录
app_dir = os.path.dirname(current_dir)  # app目录
sys.path.append(app_dir)
from langchain_core.callbacks import CallbackManagerForToolRun
from langchain_core.tools import BaseTool
import json
import requests
from langchain_core.utils import get_from_dict_or_env
from pydantic import BaseModel, ConfigDict, Field, model_validator
from utils.logger import logger
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
# 移除 lxml 依赖，使用 BeautifulSoup 的内置解析器
# from lxml.html import fromstring
import re
from bs4 import BeautifulSoup
import urllib.parse
from langchain_core.tools import tool
from typing_extensions import override
from langchain_core.messages import ToolMessage
import socket
from socket import inet_aton
from struct import unpack
from urllib.parse import urlparse
from configs.lion_config import get_value

DEFAULT_BING_SEARCH_ENDPOINT = "https://aigc.sankuai.com/v1/friday/api/search"
DEFAULT_APPID = "1872462822987792398"
DEFAULT_ENGINE_NAME = "bing-search"

"""Tool for the Bing search API."""


# 改自BingSearchAPIWrapper，因此里面的内容都以Bing命名，但是实际上起功能不仅限于Bing
class SearchAPIWrapper(BaseModel):
    """Wrapper for Bing Web Search API."""

    bing_subscription_key: str = DEFAULT_APPID # 申请到的appId
    bing_search_url: str = DEFAULT_BING_SEARCH_ENDPOINT # Friday搜索网址
    k: int = 10
    search_engine_name: str = DEFAULT_ENGINE_NAME
    search_kwargs: dict = Field(default_factory=dict)
    """Additional keyword arguments to pass to the search request."""

    model_config = ConfigDict(
        extra="forbid",
    )

    def _bing_search_results(self, search_term: str, count: int) -> List[dict]:
        # print("teststststs")
        headers = {
            "Authorization": f"Bearer {self.bing_subscription_key}",
            "Content-Type": "application/json"
        }
        params = {
            "query": search_term,
            "api": self.search_engine_name if self.search_engine_name else DEFAULT_ENGINE_NAME,
            "top_k": count,
            **self.search_kwargs,
        }
        response = requests.post(
            self.bing_search_url,
            headers=headers,
            json=params,
        )
        response.raise_for_status()
        search_results = response.json()
        if self.search_engine_name == "google-search":
            if "googleItems" in search_results:
                return search_results["googleItems"]
        elif self.search_engine_name == "bing-search":
            if "results" in search_results:
                return search_results["results"]
        # elif self.search_engine_name == "baidu-search": # 目前有问题
        #     if "results" in search_results:
        #         return search_results["results"]
        elif self.search_engine_name == "tencent-search":
            if "tencentSearchResults" in search_results:
                return list(map(lambda x: json.loads(x), search_results["tencentSearchResults"]["Response"]["Pages"]))
        elif self.search_engine_name == "wenxin-search":
            if "results" in search_results:
                return search_results["results"]
        elif self.search_engine_name == "bing-search-pro":
            if "bingSearchProResults" in search_results:
                return search_results["bingSearchProResults"]["webPages"]["value"]
        else:
            print(f"不支持的搜索引擎: {self.search_engine_name}")
        return []

    @model_validator(mode="before")
    @classmethod
    def validate_environment(cls, values: Dict) -> Any:
        """Validate that api key and endpoint exists in environment."""
        bing_subscription_key = get_from_dict_or_env(
            values, "bing_subscription_key", "BING_SUBSCRIPTION_KEY", default=DEFAULT_APPID
        )
        values["bing_subscription_key"] = bing_subscription_key

        bing_search_url = get_from_dict_or_env(
            values,
            "bing_search_url",
            "BING_SEARCH_URL",
            default=DEFAULT_BING_SEARCH_ENDPOINT,
        )

        values["bing_search_url"] = bing_search_url

        return values

    @override
    def run(self, query: str) -> str:
        """Run query through BingSearch and parse result."""
        print("Heloo")
        snippets = []
        results = self._bing_search_results(query, count=self.k)
        if len(results) == 0:
            return "No good Bing Search Result was found"
        for result in results:
            snippets.append(result["snippet"])

        return snippets

    def results(self, query: str, num_results: int) -> List[Dict]:
        """Run query through BingSearch and return metadata.

        Args:
            query: The query to search for.
            num_results: The number of results to return.

        Returns:
            A list of dictionaries with the following keys:
                snippet - The description of the result.
                title - The title of the result.
                link - The link to the result.
        """
        metadata_results = []
        results = self._bing_search_results(query, count=num_results)
        if len(results) == 0:
            return [{"Result": "No good Bing Search Result was found"}]
        for result in results:
            metadata_result = {
                "snippet": result.get("snippet", None) or result.get("content", None) or result.get("passage", None),
                "title": result.get("name", None) or result.get("title", None),
                "link": result.get("url", None) or result.get("link", None),
                "images": result.get("images", [])
            }
            metadata_results.append(metadata_result)
        return metadata_results

class SearchRun(BaseTool):
    """Tool that queries the Bing search API."""

    name: str = "bing_search"
    description: str = (
        "A wrapper around Bing Search. "
        "Useful for when you need to answer questions about current events. "
        "Input should be a search query."
    )
    api_wrapper: SearchAPIWrapper

    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> str:
        """Use the tool."""
        return self.api_wrapper.run(query)


class SearchResults(BaseTool):
    """Bing Search tool.

    Setup:
        Install ``langchain-community`` and set environment variable ``BING_SUBSCRIPTION_KEY``.

        .. code-block:: bash

            pip install -U langchain-community
            export BING_SUBSCRIPTION_KEY="your-api-key"

    Instantiation:
        .. code-block:: python

            from langchain_community.tools.bing_search import BingSearchResults
            from langchain_community.utilities import BingSearchAPIWrapper

            api_wrapper = BingSearchAPIWrapper()
            tool = BingSearchResults(api_wrapper=api_wrapper)

    Invocation with args:
        .. code-block:: python

            tool.invoke({"query": "what is the weather in SF?"})

        .. code-block:: python

            "[{'snippet': '<b>San Francisco, CA</b> <b>Weather</b> Forecast, with current conditions, wind, air quality, and what to expect for the next 3 days.', 'title': 'San Francisco, CA Weather Forecast | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/weather-forecast/347629'}, {'snippet': 'Tropical Storm Ernesto Forms; Fire <b>Weather</b> Concerns in the Great Basin: Hot Temperatures Return to the South-Central U.S. ... <b>San Francisco CA</b> 37.77°N 122.41°W (Elev. 131 ft) Last Update: 2:21 pm PDT Aug 12, 2024. Forecast Valid: 6pm PDT Aug 12, 2024-6pm PDT Aug 19, 2024 .', 'title': 'National Weather Service', 'link': 'https://forecast.weather.gov/zipcity.php?inputstring=San+Francisco,CA'}, {'snippet': 'Current <b>weather</b> <b>in San Francisco, CA</b>. Check current conditions <b>in San Francisco, CA</b> with radar, hourly, and more.', 'title': 'San Francisco, CA Current Weather | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/current-weather/347629'}, {'snippet': 'Everything you need to know about today&#39;s <b>weather</b> <b>in San Francisco, CA</b>. High/Low, Precipitation Chances, Sunrise/Sunset, and today&#39;s Temperature History.', 'title': 'Weather Today for San Francisco, CA | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/weather-today/347629'}]"

    Invocation with ToolCall:

        .. code-block:: python

            tool.invoke({"args": {"query":"what is the weather in SF?"}, "id": "1", "name": tool.name, "type": "tool_call"})

        .. code-block:: python

            ToolMessage(
                content="[{'snippet': 'Get the latest <b>weather</b> forecast for <b>San Francisco, CA</b>, including temperature, RealFeel, and chance of precipitation. Find out how the <b>weather</b> will affect your plans and activities in the city of ...', 'title': 'San Francisco, CA Weather Forecast | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/weather-forecast/347629'}, {'snippet': 'Radar. Be prepared with the most accurate 10-day forecast for <b>San Francisco, CA</b> with highs, lows, chance of precipitation from The <b>Weather</b> Channel and <b>Weather</b>.com.', 'title': '10-Day Weather Forecast for San Francisco, CA - The Weather Channel', 'link': 'https://weather.com/weather/tenday/l/San+Francisco+CA+USCA0987:1:US'}, {'snippet': 'Tropical Storm Ernesto Forms; Fire <b>Weather</b> Concerns in the Great Basin: Hot Temperatures Return to the South-Central U.S. ... <b>San Francisco CA</b> 37.77°N 122.41°W (Elev. 131 ft) Last Update: 2:21 pm PDT Aug 12, 2024. Forecast Valid: 6pm PDT Aug 12, 2024-6pm PDT Aug 19, 2024 .', 'title': 'National Weather Service', 'link': 'https://forecast.weather.gov/zipcity.php?inputstring=San+Francisco,CA'}, {'snippet': 'Current <b>weather</b> <b>in San Francisco, CA</b>. Check current conditions <b>in San Francisco, CA</b> with radar, hourly, and more.', 'title': 'San Francisco, CA Current Weather | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/current-weather/347629'}]",
                artifact=[{'snippet': 'Get the latest <b>weather</b> forecast for <b>San Francisco, CA</b>, including temperature, RealFeel, and chance of precipitation. Find out how the <b>weather</b> will affect your plans and activities in the city of ...', 'title': 'San Francisco, CA Weather Forecast | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/weather-forecast/347629'}, {'snippet': 'Radar. Be prepared with the most accurate 10-day forecast for <b>San Francisco, CA</b> with highs, lows, chance of precipitation from The <b>Weather</b> Channel and <b>Weather</b>.com.', 'title': '10-Day Weather Forecast for San Francisco, CA - The Weather Channel', 'link': 'https://weather.com/weather/tenday/l/San+Francisco+CA+USCA0987:1:US'}, {'snippet': 'Tropical Storm Ernesto Forms; Fire <b>Weather</b> Concerns in the Great Basin: Hot Temperatures Return to the South-Central U.S. ... <b>San Francisco CA</b> 37.77°N 122.41°W (Elev. 131 ft) Last Update: 2:21 pm PDT Aug 12, 2024. Forecast Valid: 6pm PDT Aug 12, 2024-6pm PDT Aug 19, 2024 .', 'title': 'National Weather Service', 'link': 'https://forecast.weather.gov/zipcity.php?inputstring=San+Francisco,CA'}, {'snippet': 'Current <b>weather</b> <b>in San Francisco, CA</b>. Check current conditions <b>in San Francisco, CA</b> with radar, hourly, and more.', 'title': 'San Francisco, CA Current Weather | AccuWeather', 'link': 'https://www.accuweather.com/en/us/san-francisco/94103/current-weather/347629'}],
                name='bing_search_results_json',
                tool_call_id='1'
            )

    """  # noqa: E501

    name: str = "bing_search_results_json"
    description: str = (
        "A wrapper around Bing Search. "
        "Useful for when you need to answer questions about current events. "
        "Input should be a search query. Output is an array of the query results."
    )
    num_results: int = 4
    """Max search results to return, default is 4."""
    api_wrapper: SearchAPIWrapper
    response_format: Literal["content_and_artifact"] = "content_and_artifact"

    def _run(
        self,
        query: str,
        run_manager: Optional[CallbackManagerForToolRun] = None,
    ) -> Tuple[str, List[Dict]]:
        """Use the tool."""
        try:
            logger.info(f"🔍 开始执行{self.name}搜索工具, query: {query}, 结果数量: {self.num_results}")
            
            logger.debug(f"📞 调用搜索API接口: {self.api_wrapper.bing_search_url}")
            results = self.api_wrapper.results(query, self.num_results)
            
            logger.info(f"✅ {self.name}搜索完成，返回 {len(results)} 条结果")
            if results:
                logger.debug(f"📋 第一条结果预览: {results[0].get('title', '无标题')[:50]}...")
            
            return str(results), results
        except Exception as e:
            error_msg = f"搜索工具执行失败: {str(e)}"
            logger.error(f"❌ {self.name}搜索引擎调用失败: {str(e)}")
            # 返回空结果而不是异常信息，确保工作流程能继续
            return "[]", []

    def _set_num_results(self, num_results: int):
        self.num_results = num_results

def _init_search_api_wrapper(search_engine_name: str) -> SearchAPIWrapper:
    logger.info(f"开始初始化bing_search_results_json, search_engine_name: {search_engine_name}")
    return SearchAPIWrapper(
        bing_subscription_key=DEFAULT_APPID,
        bing_search_url=DEFAULT_BING_SEARCH_ENDPOINT,
        k=1,
        search_engine_name=search_engine_name
    )

def _get_encoding_from_response(response: requests.Response) -> str | None:
    """从requests的Response对象中获取或猜测编码"""
    # 1. 从 HTTP headers 的 Content-Type 获取
    content_type = response.headers.get('content-type')
    if content_type:
        charset_match = re.search(r'charset=([\w-]+)', content_type, re.I)
        if charset_match:
            encoding = charset_match.group(1).lower()
            # 过滤掉一些明显错误的或不常见的编码
            if encoding not in ['iso-8859-1', 'gb2312']: # 有些网站错误声明为这些，但实际是utf-8
                try: # 验证编码是否有效
                    "test".encode(encoding)
                    logger.info(f"从 HTTP header 获取编码: {encoding}")
                    return encoding
                except LookupError:
                    logger.warning(f"HTTP header 中的编码无效: {encoding}")
    
    # 2. requests 自身的编码猜测 (response.apparent_encoding 使用了 chardet)
    # apparent_encoding 通常比 encoding 更可靠，因为它会分析内容
    if response.apparent_encoding:
        logger.info(f"使用 requests.apparent_encoding: {response.apparent_encoding}")
        return response.apparent_encoding.lower()
        
    # 3. 如果都获取不到，可以尝试用 cchardet 对内容进行检测
    # 注意：这里我们还没有解码，所以 response.content 是 bytes
    # 但是如果走到这一步，说明 requests 也没好办法了
    # 有些网站可能 header 和 apparent_encoding 都是错的
    # logger.info("尝试 cchardet 检测 response.content")
    # detection = cchardet.detect(response.content)
    # if detection and detection['encoding']:
    #     logger.info(f"cchardet 检测编码: {detection['encoding']} (confidence: {detection['confidence']})")
    #     return detection['encoding'].lower()

    logger.warning("无法确定编码，将使用 UTF-8 作为默认尝试。")
    return 'utf-8' # 最后的备选

def has_excessive_replacement_chars(text: str, threshold_ratio: float = 0.01, min_length_for_check: int = 100) -> bool:
    """
    检查文本中 Unicode 替换字符 (�) 的比例是否超过阈值。

    Args:
        text: 需要检查的文本字符串。
        threshold_ratio: 替换字符所占比例的阈值。默认为 0.01 (1%)。
        min_length_for_check: 文本达到此长度才进行检查，以避免对过短文本的误判。默认为100。

    Returns:
        True 如果替换字符比例超过阈值，否则 False。
    """
    REPLACEMENT_CHAR = '\ufffd' # 替换字符
    if not text or len(text) < min_length_for_check:
        return False # 对于空字符串或过短的字符串，不认为是"过多"替换字符

    replacement_count = text.count(REPLACEMENT_CHAR)
    ratio = replacement_count / len(text)
    
    is_excessive = ratio > threshold_ratio
    if is_excessive:
        logger.debug(f"检测到过多替换字符: 数量={replacement_count}, 总长={len(text)}, 比例={ratio:.4f} (阈值: {threshold_ratio:.4f})")
        
    return is_excessive

def check_ssrf(url):
    hostname = urlparse(url).hostname

    def ip2long(ip_addr):
        return unpack("!L", inet_aton(ip_addr))[0]

    def is_inner_ipaddress(ip):
        ip = ip2long(ip)
        return ip2long('*********') >> 24 == ip >> 24 or \
               ip2long('**********') >> 20 == ip >> 20 or \
               ip2long('***********') >> 16 == ip >> 16 or \
               ip2long('0.0.0.0') >> 24 == ip >> 24

    try:
        # if not re.match(r"^https?://.*/.*$", url):
        #     raise BaseException("url format error")
        ip_address = socket.getaddrinfo(hostname, 'http')[0][4][0]
        if is_inner_ipaddress(ip_address):
            raise BaseException("inner ip address attack")
        return True, "success"
    except BaseException as e:
        return False, str(e)
    except:
        return False, "unknow error"

def _extract_key_content(url: str, related_info: List[Any], index: int, pdf_only: bool, need_conclude: bool) -> None:
        """
        提取网页关键内容
        
        Args:
            url: 网页URL
            related_info: 存储爬取结果的数组
            query: 搜索关键词
            index: 当前网页索引
            pdf_only: 是否只爬取PDF文件
            need_conclude: 是否需要总结
        """
        if not url:
            logger.debug(f"🚫 [{index+1}] URL为空，跳过爬取")
            related_info[index] = ["", "None", "", "", ""]
            return
        
        try:
            if not check_ssrf(url):
                logger.debug(f"⚠️ [{index+1}] 检测到SSRF攻击，跳过爬取")
                related_info[index] = ["", "None", "", "", ""]
                return
            else:
                logger.debug(f"✅ [{index+1}] 未检测到SSRF攻击，继续爬取")
        except Exception as e:
            logger.debug(f"⚠️ [{index+1}] 检测到SSRF攻击，跳过爬取: {e}")
            related_info[index] = ["", "None", "", "", ""]
            return
            
        # 如果是PDF文件且pdf_only为False，或者不是PDF文件且pdf_only为True，则跳过
        is_pdf = url.lower().endswith('.pdf')
        if (is_pdf and not pdf_only) or (not is_pdf and pdf_only):
            logger.debug(f"⏭️ [{index+1}] 根据pdf_only={pdf_only}设置跳过URL: {url}, 是否为PDF: {is_pdf}")
            related_info[index] = ["", "None", "", "", ""]
            return
            
        logger.debug(f"🌐 [{index+1}] 开始爬取: {url}")
        


        try:
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
                'Accept-Encoding': 'gzip, deflate, br', # 明确接受压缩，requests会自动处理解压
            }
            
            parsed_url = urllib.parse.urlparse(url)
            if not parsed_url.scheme:
                url = "http://" + url
                
            logger.debug(f"📡 [{index+1}] 发送HTTP请求: {url}")
            # 调整超时时间，适应网络受限环境
            response = requests.get(url, headers=headers, timeout=10, stream=True) # 减少超时时间从15秒到10秒
            response.raise_for_status()
            logger.debug(f"✅ [{index+1}] HTTP响应成功，状态码: {response.status_code}")
            
            if url.lower().endswith('.pdf'):
                logger.debug(f"📄 [{index+1}] 检测到PDF文件，暂不支持处理")
                related_info[index] = [f"[PDF文件，无法直接提取内容] {url}", "PDF文件内容需要专门处理", "", "", ""]
                return
                
            # --- 关键的编码处理 ---
            logger.debug(f"🔤 [{index+1}] 开始分析网页编码")
            # 1. 从响应头或内容猜测编码
            encoding = _get_encoding_from_response(response)
            
            # 2. 使用获取到的编码解码 response.content
            try:
                # response.content 是原始字节
                html_content_bytes = response.content # 读取所有内容
                html_str = html_content_bytes.decode(encoding, errors='replace')
                logger.debug(f"✅ [{index+1}] 使用编码 '{encoding}' 解码成功，内容长度: {len(html_str)}")
            except Exception as e:
                logger.debug(f"❌ [{index+1}] 使用编码 '{encoding}' 解码失败: {e}. 尝试UTF-8")
                # 如果指定的编码解码失败，尝试用UTF-8（最常见的）
                try:
                    html_str = response.content.decode('utf-8', errors='replace')
                    encoding = 'utf-8' # 更新实际使用的编码
                    logger.debug(f"✅ [{index+1}] 使用UTF-8解码成功")
                except Exception as e_utf8:
                    logger.error(f"💥 [{index+1}] 使用UTF-8解码也失败: {e_utf8}")
                    related_info[index] = [f"解码失败: {url}", "None", "", "", ""]
                    return
            # --- 编码处理结束 ---

            # 现在 html_str 是一个解码后的字符串

            # 使用 BeautifulSoup 解析 HTML（移除 lxml 依赖）
            try:
                logger.debug(f"🔧 [{index+1}] 开始使用BeautifulSoup解析HTML")
                # 使用BeautifulSoup解析已经解码的 html_str
                soup = BeautifulSoup(html_str, 'html.parser')

                # 移除脚本和样式标签
                for script_or_style in soup(["script", "style"]):
                    script_or_style.extract()

                # 提取文本内容
                text = soup.get_text(separator='\n')
                lines = [line.strip() for line in text.splitlines() if line.strip()]
                raw_text = '\n'.join(lines)

                # 清理多余的空白字符
                raw_text = re.sub(r'\s+', ' ', raw_text).strip()

                if has_excessive_replacement_chars(raw_text):
                    logger.debug(f"⚠️ [{index+1}] 检测到过多替换字符，识别为无效字符串")
                    related_info[index] = [f"爬取到无效字符串: {url}", "None", "", "", ""]
                    return

                if len(raw_text) < 50:
                    logger.debug(f"⚠️ [{index+1}] 提取内容过短 ({len(raw_text)} 字符)，可能是无效页面")
                    related_info[index] = [raw_text, "None", "", "", ""]
                    return

                # 限制长度
                summary = raw_text[:1000] if len(raw_text) > 1000 else raw_text
                related_info[index] = [raw_text, summary, "", "", ""]
                logger.debug(f"✅ [{index+1}] BeautifulSoup解析成功，原始长度: {len(raw_text)}, 摘要长度: {len(summary)}")

            except Exception as e_bs:
                logger.debug(f"💥 [{index+1}] BeautifulSoup解析失败: {e_bs}")
                related_info[index] = [f"解析失败: {url}", "None", "", "", ""]

        except requests.exceptions.RequestException as e_req:
            logger.debug(f"🌐 [{index+1}] 请求网页失败: {str(e_req)}")
            related_info[index] = [f"请求失败: {url}, 错误: {str(e_req)}", "None", "", "", ""]
        except Exception as e_global:
            logger.debug(f"💥 [{index+1}] 提取网页内容时发生未知错误: {str(e_global)}")
            related_info[index] = [f"未知错误: {url}, 错误: {str(e_global)}", "None", "", "", ""]


def run_crawl_page(search_results: List[Dict[str, Any]], 
                      pdf_only: bool = False, need_conclude: bool = True) -> None:
        """
        爬取搜索结果页面内容
        
        Args:
            query: 搜索关键词
            search_results: 搜索结果列表
            pdf_only: 是否只爬取PDF文件
            need_conclude: 是否需要总结
        """
        if not search_results:
            logger.warning("⚠️ 没有搜索结果需要爬取")
            return
            
        logger.info(f"🌐 开始爬取搜索结果, 共 {len(search_results)} 个网页")
        logger.info(f"⚙️ 爬取配置: pdf_only={pdf_only}, need_conclude={need_conclude}")
        
        # 创建数组存储爬取结果
        related_info = [0] * len(search_results)
        
        # 使用线程池并发爬取网页
        start_time = time.time()
        successful_crawls = 0
        
        logger.info(f"🧵 启动线程池，最大并发数: {min(len(search_results), 8)}")
        
        with ThreadPoolExecutor(max_workers=min(len(search_results), 8)) as executor:
            # 提交所有爬取任务
            futures = [
                executor.submit(
                    _extract_key_content, 
                    result.get('link', ''), 
                    related_info,  
                    i, 
                    pdf_only, 
                    need_conclude
                ) 
                for i, result in enumerate(search_results)
            ]
            
            logger.info(f"📋 已提交 {len(futures)} 个爬取任务到线程池")
            
            try:
                # 等待所有线程完成，设置超时时间 - 调整为更长的超时时间
                completed_tasks = 0
                for future in as_completed(futures, timeout=60):
                    try:
                        future.result()  # 获取结果，如果有异常会抛出
                        completed_tasks += 1
                        if completed_tasks % 5 == 0:  # 每完成5个任务记录一次进度
                            logger.info(f"📈 爬取进度: {completed_tasks}/{len(futures)} 个任务已完成")
                    except Exception as e:
                        logger.debug(f"🔧 单个爬取任务执行失败: {str(e)}")
                        pass  # 单个任务失败不影响整体流程
                        
                logger.info(f"✅ 所有爬取任务完成，共处理 {completed_tasks}/{len(futures)} 个任务")
                        
            except Exception as e:
                logger.warning(f"⏰ 并发爬取部分任务超时: {str(e)}")
                # 不要立即取消所有任务，而是等待已经在执行的任务
                completed_count = sum(1 for f in futures if f.done())
                logger.info(f"📊 爬取超时统计：已完成 {completed_count}/{len(futures)} 个任务")
        
        end_time = time.time()
        logger.info(f"⏱️ 网页爬取完成，总耗时 {end_time - start_time:.2f} 秒")
        
        # 处理爬取结果
        valid_content_count = 0
        failed_quality_count = 0
        failed_format_count = 0
        failed_total_count = 0
        
        logger.info("🔍 开始分析爬取结果...")
        
        for i, result in enumerate(search_results):
            if i < len(related_info) and related_info[i] != 0:
                # 解析爬取结果
                # related_info[i] 格式: [原始文本, 提取/摘要文本, PDF封面图片, PDF图片列表, 网页图片列表]
                if isinstance(related_info[i], list) and len(related_info[i]) >= 2:
                    raw_text, extracted_text = related_info[i][0:2]
                    
                    
                    # 检查内容质量
                    if extracted_text and extracted_text != 'None':
                        result['crawled_content'] = raw_text
                        result['content_summary'] = extracted_text

                        
                        # 如果有图片，也添加到结果中
                        if len(related_info[i]) > 2 and related_info[i][2]:
                            result['pdf_cover_image'] = related_info[i][2]
                        
                        if len(related_info[i]) > 3 and related_info[i][3]:
                            result['pdf_images'] = related_info[i][3]
                            
                        if len(related_info[i]) > 4 and related_info[i][4]:
                            result['web_images'] = related_info[i][4]
                            
                        valid_content_count += 1
                        logger.debug(f"✅ [{i+1}] 成功爬取: {result.get('title', '')[:30]}..., 摘要长度: {len(extracted_text)}")
                    else:
                        failed_quality_count += 1
                        logger.debug(f"⚠️ [{i+1}] 内容质量不佳，使用搜索摘要: {result.get('title', '')[:30]}...")
                        result['crawl_status'] = 'failed_quality'
                        # 爬取失败时，使用搜索摘要作为备用内容
                        result['crawled_content'] = result.get('snippet', '')
                        result['content_summary'] = result.get('snippet', '')
                else:
                    failed_format_count += 1
                    logger.debug(f"❌ [{i+1}] 格式错误，使用搜索摘要: {result.get('title', '')[:30]}...")
                    result['crawl_status'] = 'failed_format'
                    # 爬取失败时，使用搜索摘要作为备用内容
                    result['crawled_content'] = result.get('snippet', '')
                    result['content_summary'] = result.get('snippet', '')
            else:
                failed_total_count += 1
                logger.debug(f"💥 [{i+1}] 爬取失败，使用搜索摘要: {result.get('title', '')[:30]}...")
                result['crawl_status'] = 'failed'
                # 爬取失败时，使用搜索摘要作为备用内容
                result['crawled_content'] = result.get('snippet', '')
                result['content_summary'] = result.get('snippet', '')
        
        # 如果所有爬取都失败，至少保证有搜索摘要内容
        if valid_content_count == 0:
            logger.warning("⚠️ 所有网页爬取都失败，正在应用搜索摘要降级策略")
            for result in search_results:
                if not result.get('crawled_content'):
                    result['crawled_content'] = result.get('snippet', '')
                    result['content_summary'] = result.get('snippet', '')
        
        logger.info(f"📊 爬取结果统计:")
        logger.info(f"   ✅ 成功爬取: {valid_content_count} 个")
        logger.info(f"   ⚠️ 质量问题: {failed_quality_count} 个")
        logger.info(f"   ❌ 格式错误: {failed_format_count} 个")
        logger.info(f"   💥 完全失败: {failed_total_count} 个")
        logger.info(f"   📋 总计处理: {len(search_results)} 个")
        
        if valid_content_count > 0:
            success_rate = (valid_content_count / len(search_results)) * 100
            logger.info(f"🎯 爬取成功率: {success_rate:.1f}%")
        else:
            logger.warning("🚨 爬取成功率: 0% (已启用降级策略)")

searchToolDict:Dict[str, BaseTool] = {
    "bing_search": SearchResults(
        name="bing_search",
        description="用于Bing搜索",
        api_wrapper=_init_search_api_wrapper("bing-search"),
        response_format="content_and_artifact"
    ),
    "google_search": SearchResults(
        name="google_search",
        description="用于Google搜索",
        api_wrapper=_init_search_api_wrapper("google-search"),
        response_format="content_and_artifact"
    ),
    "wenxin_search": SearchResults(
        name="wenxin_search",
        description="用于文心搜索",
        api_wrapper=_init_search_api_wrapper("wenxin-search"),
        response_format="content_and_artifact"
    ),
    "tencent_search": SearchResults(
        name="tencent_search",
        description="用于腾讯搜索",
        api_wrapper=_init_search_api_wrapper("tencent-search"),
        response_format="content_and_artifact"
    ),
    "bing_search_pro": SearchResults(
        name="bing_search_pro",
        description="用于Bing专业搜索",
        api_wrapper=_init_search_api_wrapper("bing-search-pro"),
        response_format="content_and_artifact"
    )
}

def init_search_tools(tools_config):
    using_engines = []
    for engine_name, engine_status in tools_config["SearchEngineStatus"].items():
        if engine_status:
            using_engines.append(engine_name)

    logger.info(f"初始化搜索工具，使用以下搜索引擎: {using_engines}")

    # 从Lion配置中心获取工具描述，如果没有配置则使用默认值
    tool_description = str(get_value("xiaomei.humanrelation.search_tool_description", "联网搜索，用于获取新闻信息，你不知道的知识都可以使用"))
    logger.info(f"🔧 搜索工具描述配置: {tool_description}")

    @tool(description=tool_description)
    def web_search(query: str) -> str:
        """
        联网搜索，可以用于搜集信息数据
        """
        logger.info(f"🔍 开始执行web_search工具，查询关键词: '{query}'")
        logger.info(f"📋 配置的搜索引擎: {using_engines}")
        
        try:
            results = []
            total_results_count = 0
            
            for i, engine_name in enumerate(using_engines, 1):
                logger.info(f"🚀 [{i}/{len(using_engines)}] 正在调用搜索引擎: {engine_name}")
                try:
                    content, artifact = searchToolDict[engine_name]._run(query)
                    logger.info(f"✅ 搜索引擎 {engine_name} 返回了 {len(artifact)} 条结果")
                    if artifact:  # 只有当有结果时才记录
                        logger.info(f"📄 搜索引擎 {engine_name} 返回的第1条结果标题: {artifact[0].get('title', '无标题')}")
                        results.extend(artifact)
                        total_results_count += len(artifact)
                    else:
                        logger.warning(f"⚠️ 搜索引擎 {engine_name} 返回空结果")
                except Exception as e:
                    logger.error(f"❌ 搜索引擎 {engine_name} 执行失败: {str(e)}")
                    continue  # 继续尝试其他搜索引擎
            
            logger.info(f"📊 搜索阶段完成，总共获得 {total_results_count} 条搜索结果")
            
            if not results:
                logger.warning("⚠️ 所有搜索引擎都未返回结果")
                return "搜索未返回任何结果，请尝试使用不同的关键词。"
            
            # print(results[0])
            logger.info(f"🕷️ 开始爬取阶段，准备爬取 {len(results)} 个网页")
            run_crawl_page(results)
            logger.info("🕷️ 爬取阶段完成")
            
            # 优化返回逻辑：优先返回爬取内容，如果没有则返回搜索摘要
            content_parts = []
            crawled_count = 0
            fallback_count = 0
            
            # 🎯 添加随机采样逻辑，限制返回结果数量以避免上下文过长
            max_results = 5  # 最多保留5条结果
            if len(results) > max_results:
                logger.info(f"🎲 总共有 {len(results)} 条结果，保留前 {max_results} 条以避免上下文过长")
                # 随机采样，保持结果的多样性
                sampled_results = results[:max_results]
            else:
                sampled_results = results
                logger.info(f"📊 结果数量 {len(results)} 条，无需采样")
            
            for i, result in enumerate(sampled_results, 1):
                # 优先使用爬取的内容，如果没有则使用搜索摘要
                content = result.get("crawled_content", "") or result.get("snippet", "")
                if content:
                    # 统计内容来源
                    if result.get("crawled_content"):
                        crawled_count += 1
                        logger.debug(f"📝 [{i}] 使用爬取内容: {result.get('title', '无标题')[:30]}...")
                    else:
                        fallback_count += 1
                        logger.debug(f"📋 [{i}] 使用搜索摘要: {result.get('title', '无标题')[:30]}...")
                    
                    # 添加标题和链接信息以便用户识别来源
                    title = result.get("title", "无标题")
                    link = result.get("link", "")
                    content_with_meta = f"【{title}】\n{content}\n来源: {link}\n"
                    content_parts.append(content_with_meta)
            
            logger.info(f"📈 内容统计: 成功爬取内容 {crawled_count} 个，使用搜索摘要 {fallback_count} 个")
            
            if not content_parts:
                logger.warning("⚠️ 未能获取到任何有效内容")
                return "搜索完成，但未能获取到有效内容。"
                
            final_content = '\n'.join(content_parts)
            content_length = len(final_content)
            logger.info(f" web_search执行完成！返回内容总长度: {content_length} 字符")
            logger.info(f" 返回内容预览: {final_content[:100]+'...' if len(final_content) > 100 else final_content}")
            if not final_content.strip():
                logger.warning("⚠️ 返回内容为空")
                return "我进行了网络搜索，但未能找到相关的详细信息。您想尝试其他关键词，或者我可以用已知的信息来回答您的问题吗？"
            return final_content
        
        except Exception as e:
            logger.error(f"💥 web_search 工具执行过程中发生异常: {str(e)}")
            return f"搜索过程中遇到技术问题，但我会尽力为您提供帮助。请问您具体想了解什么信息？"
    return web_search

if __name__ == "__main__":
    # result = bingSearchTool.invoke("查询北京天气")
    # print(result)
    test = init_search_tools({"SearchEngineStatus": {"bing_search": True, "google_search": False, "wenxin_search": False, "tencent_search": False, "bing_search_pro": False}})
    # result = test("查询北京天气")
    print(test.get_input_jsonschema())
    # print(result)
    # print(list(searchToolDict.values()))