import os
import sys
import socket
import contextvars
import random
from loguru import logger as loguru_logger
from configs.config import APP_KEY

# 创建一个 ContextVar 用于存储 traceId
trace_id_var = contextvars.ContextVar("trace_id", default=None)

if sys.platform == "linux":
    LOG_PATH = f"/opt/logs/{APP_KEY}/app.log"
else:
    current_dir = os.path.dirname(os.path.abspath(__file__))
    LOG_PATH = os.path.join(current_dir, "..", "..", "data", "applogs", APP_KEY, "app.log")

HOSTNAME = socket.gethostname()
LOG_FORMAT = "{time:YYYY-MM-DD HH:mm:ss} " + f"{HOSTNAME} {APP_KEY}" \
             + " {level} {name} {function} {extra[trace_id]} {message}"


# 创建一个函数来生成新的 traceId
def generate_trace_id():
    """生成唯一的trace_id"""
    return str(random.randint(int(1e18), int(1e19) - 1))


# 创建一个函数来设置 traceId
def set_trace_id(request):
    if request:
        trace_id = request.headers.get("m-traceid")
        if not trace_id:
            trace_id = generate_trace_id()
    else:
        trace_id = generate_trace_id()
    trace_id_var.set(trace_id)


def update_trace_id(trace_id):
    trace_id_var.set(trace_id)


# 创建一个函数来获取当前的 traceId
def get_trace_id():
    return trace_id_var.get()


# 创建一个绑定 traceId 的 logger
def get_logger_with_trace_id():
    trace_id = get_trace_id()
    return loguru_logger.bind(trace_id=trace_id)


# 封装 logger，使其自动绑定 traceId
class LoggerWrapper:
    def __getitem__(self, name):
        return getattr(get_logger_with_trace_id(), name)
    
    def __getattr__(self, name):
        # 获取绑定了 traceId 的 logger
        return getattr(get_logger_with_trace_id(), name)


# 导出封装的 logger
logger = LoggerWrapper()
logger.add(LOG_PATH, rotation="100MB", level="INFO",
           format=LOG_FORMAT)