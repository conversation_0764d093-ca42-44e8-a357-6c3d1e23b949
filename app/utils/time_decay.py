import datetime

def calculate_exponential_decay(timestamp: str, half_life_days: float) -> float:
    """
    基于时间差计算指数衰减因子
    
    Args:
        timestamp: 时间戳，格式为 'YYYY-MM-DD-HH-MM'
        half_life_days: 半衰期常数，单位为天
        
    Returns:
        float: 指数衰减因子（介于0和1之间）
    """
    # 解析输入的时间戳
    try:
        dt = datetime.datetime.strptime(timestamp, "%Y-%m-%d-%H-%M")
    except ValueError:
        raise ValueError("Invalid timestamp format. Expected format: YYYY-MM-DD-HH-MM")
    
    # 获取当前时间
    current_time = datetime.datetime.now()
    
    # 计算时间差，单位为天
    time_diff_days = (current_time - dt).total_seconds() / (24 * 3600)
    
    # 确保time_diff不为负（对于未来的时间戳）
    time_diff_days = max(0, time_diff_days)
    
    # 使用指数衰减公式计算衰减因子：exp(-ln(2) * t/half_life)
    # half_life是值减半所需的时间
    decay_factor = 2 ** (-time_diff_days / half_life_days)
    
    return decay_factor 