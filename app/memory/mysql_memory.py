from __future__ import annotations

import asyncio
import json
from typing import Any, AsyncIterator, List, Optional, Tuple, Union

from langchain_core.runnables import RunnableConfig
from sqlalchemy import create_engine, text, inspect
from langgraph.checkpoint.base import BaseCheckpointSaver, Checkpoint, CheckpointTuple
from langgraph.checkpoint.serde.jsonplus import JsonPlusSerializer

from my_mysql.sql_client import CLIENT, select_one, select_many, execute
from utils.logger import logger

class MySQLCheckpointSerializer(JsonPlusSerializer):
    """序列化器，用于在JSON和对象之间转换，兼容MySQL。"""
    def dumps(self, obj: Any) -> str:
        """将对象序列化为JSON字符串。"""
        return super().dumps(obj)

    def loads(self, s: str) -> Any:
        """将JSON字符串反序列化为对象。"""
        if s is None:
            return None
        return super().loads(s)

class MySQLCheckpointSaver(BaseCheckpointSaver):
    """一个基于MySQL的LangGraph检查点保存器。"""

    serde = MySQLCheckpointSerializer()

    def __init__(self):
        super().__init__()
        self.create_table_if_not_exists()

    def create_table_if_not_exists(self):
        """如果表不存在，则创建chat_history表。"""
        with CLIENT.connect() as conn:
            # Use raw SQL query for compatibility, as inspect() fails with zebraproxy
            check_table_sql = text("SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = 'chat_history'")
            result = conn.execute(check_table_sql).fetchone()
            
            if not result:
                logger.info("表 'chat_history' 不存在，正在创建...")
                table_creation_sql = text("""
                CREATE TABLE chat_history (
                    user_id VARCHAR(255) NOT NULL,
                    thread_id VARCHAR(255) NOT NULL,
                    thread_ts VARCHAR(255) NOT NULL,
                    parent_ts VARCHAR(255),
                    checkpoint LONGTEXT,
                    metadata LONGTEXT,
                    PRIMARY KEY (user_id, thread_id, thread_ts)
                );
                """)
                conn.execute(table_creation_sql)
                conn.commit()
                logger.info("表 'chat_history' 创建成功。")

    def get_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        thread_id = config["configurable"]["thread_id"]
        user_id = config["configurable"]["user_id"]
        thread_ts = config["configurable"].get("thread_ts")

        if thread_ts:
            sql = text("SELECT checkpoint, parent_ts, metadata FROM chat_history WHERE user_id = :user_id AND thread_id = :thread_id AND thread_ts = :thread_ts")
            result = select_one(sql.bindparams(user_id=user_id, thread_id=thread_id, thread_ts=thread_ts))
            if result:
                return CheckpointTuple(
                    config={"configurable": {"thread_id": thread_id, "thread_ts": thread_ts, "user_id": user_id}},
                    checkpoint=self.serde.loads(result[0]),
                    metadata=self.serde.loads(result[2]),
                    parent_config={"configurable": {"thread_id": thread_id, "thread_ts": result[1], "user_id": user_id}} if result[1] else None
                )
        else:
            sql = text("SELECT checkpoint, thread_ts, parent_ts, metadata FROM chat_history WHERE user_id = :user_id AND thread_id = :thread_id ORDER BY thread_ts DESC LIMIT 1")
            result = select_one(sql.bindparams(user_id=user_id, thread_id=thread_id))
            if result:
                return CheckpointTuple(
                    config={"configurable": {"thread_id": thread_id, "thread_ts": result[1], "user_id": user_id}},
                    checkpoint=self.serde.loads(result[0]),
                    metadata=self.serde.loads(result[3]),
                    parent_config={"configurable": {"thread_id": thread_id, "thread_ts": result[2], "user_id": user_id}} if result[2] else None,
                )
        return None

    def list(self, config: RunnableConfig) -> List[RunnableConfig]:
        thread_id = config["configurable"]["thread_id"]
        user_id = config["configurable"]["user_id"]
        sql = text("SELECT thread_id, thread_ts, user_id FROM chat_history WHERE user_id = :user_id AND thread_id = :thread_id ORDER BY thread_ts ASC")
        results = select_many(sql.bindparams(user_id=user_id, thread_id=thread_id))
        return [
            {"configurable": {"thread_id": row[0], "thread_ts": row[1], "user_id": row[2]}} for row in results
        ]

    def put(self, config: RunnableConfig, checkpoint: Union[Checkpoint, str], metadata: dict, *args, **kwargs) -> RunnableConfig:
        """Saves a checkpoint, accepting either a dict or its serialized string representation."""
        if isinstance(checkpoint, str):
            if not checkpoint.strip():
                return config # 忽略空的检查点字符串
            
            try:
                # 尝试将字符串解析为JSON，如果失败，则认为它是一个控制消息并忽略
                checkpoint_obj = self.serde.loads(checkpoint)
                checkpoint_str = checkpoint
            except json.JSONDecodeError:
                logger.warning(f"忽略无法解析的检查点字符串（可能为LangGraph控制消息）: '{checkpoint}'")
                return config # 返回原始配置，不做任何操作
        else:
            checkpoint_obj = checkpoint
            checkpoint_str = self.serde.dumps(checkpoint_obj)

        thread_id = config["configurable"]["thread_id"]
        user_id = config["configurable"]["user_id"]
        # The 'ts' might not exist in control messages that we now ignore.
        # So we should only access it after we know we have a valid checkpoint object.
        if "ts" not in checkpoint_obj:
             logger.warning(f"检查点对象中缺少 'ts' 字段，无法保存: {checkpoint_obj}")
             return config
        
        thread_ts = checkpoint_obj["ts"]

        parent_tuple = self.get_tuple({"configurable": {"thread_id": thread_id, "user_id": user_id}})
        parent_ts = parent_tuple.config["configurable"]["thread_ts"] if parent_tuple else None

        sql = text("""
            INSERT INTO chat_history (user_id, thread_id, thread_ts, parent_ts, checkpoint, metadata)
            VALUES (:user_id, :thread_id, :thread_ts, :parent_ts, :checkpoint, :metadata)
            ON DUPLICATE KEY UPDATE parent_ts = :parent_ts, checkpoint = :checkpoint, metadata = :metadata;
        """)
        
        execute(sql.bindparams(
            user_id=user_id,
            thread_id=thread_id,
            thread_ts=thread_ts,
            parent_ts=parent_ts,
            checkpoint=checkpoint_str,
            metadata=self.serde.dumps(metadata)
        ))
        
        return {"configurable": {"thread_id": thread_id, "thread_ts": thread_ts, "user_id": user_id}}

    def put_writes(
        self,
        config: RunnableConfig,
        writes: List[Tuple[Checkpoint, dict]],
        *args,
        **kwargs,
    ) -> RunnableConfig:
        """保存一个检查点写入列表。"""
        for checkpoint, metadata in writes:
            config = self.put(config, checkpoint, metadata)
        return config

    def get_history(self, conversation_id: str, user_id: str) -> List[dict]:
        """获取指定对话的完整消息历史，过滤掉系统消息和重复的用户输入"""
        latest_checkpoint_tuple = self.get_tuple({"configurable": {"thread_id": conversation_id, "user_id": user_id}})
        
        history = []
        if latest_checkpoint_tuple and latest_checkpoint_tuple.checkpoint:
            messages = latest_checkpoint_tuple.checkpoint.get('channel_values', {}).get('messages', [])
            
            for msg in messages:
                # 只保留用户(human)和AI的消息
                if msg.type in ['human', 'ai']:
                    # 避免因内部循环导致重复记录用户输入
                    if not history or not (msg.type == history[-1]['type'] and msg.content == history[-1]['content']):
                        history.append({
                            "type": msg.type,
                            "content": msg.content,
                            "additional_kwargs": msg.additional_kwargs
                        })
        return history 

    def get_conversation_ids(self, user_id: str) -> List[str]:
        """获取指定用户的所有对话ID列表。"""
        sql = text("SELECT DISTINCT thread_id FROM chat_history WHERE user_id = :user_id ORDER BY thread_id DESC")
        results = select_many(sql.bindparams(user_id=user_id))
        return [row[0] for row in results]

    async def aget_tuple(self, config: RunnableConfig) -> Optional[CheckpointTuple]:
        return await asyncio.get_running_loop().run_in_executor(None, self.get_tuple, config)

    async def alist(self, config: RunnableConfig) -> AsyncIterator[RunnableConfig]:
        results = await asyncio.get_running_loop().run_in_executor(None, self.list, config)
        for res in results:
            yield res

    async def aput(self, config: RunnableConfig, checkpoint: Checkpoint, metadata: dict, *args, **kwargs) -> RunnableConfig:
        return await asyncio.get_running_loop().run_in_executor(None, self.put, config, checkpoint, metadata, *args, **kwargs)

    async def aput_writes(
        self,
        config: RunnableConfig,
        writes: List[Tuple[Checkpoint, dict]],
        *args,
        **kwargs,
    ) -> RunnableConfig:
        """异步接口：保存一个检查点写入列表。"""
        return await asyncio.get_running_loop().run_in_executor(
            None, self.put_writes, config, writes, *args, **kwargs
        ) 