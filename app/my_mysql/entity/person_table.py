########################################################
# 人员表处理脚本
# 对应ES索引：memory_person_store
########################################################

import json
from typing import List,Optional
from my_mysql import sql_client
from sqlalchemy import Table,Column,Integer,String,MetaData,insert,update,select,text,TIMESTAMP,Index,and_,Text,Boolean,or_
from sqlalchemy.dialects.mysql import BIGINT,TINYINT
from sqlalchemy.exc import SQLAlchemyError
from utils.logger import logger

"""
CREATE TABLE `person_memory` (
  `person_id` varchar(128) COLLATE utf8mb4_general_ci NOT NULL COMMENT '人员唯一标识',
  `user_id` varchar(255) COLLATE utf8mb4_general_ci NULL COMMENT '关联的用户ID',
  `is_user` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否为用户',
  `canonical_name` varchar(256) COLLATE utf8mb4_general_ci NOT NULL COMMENT '正式姓名',
  `aliases` text COLLATE utf8mb4_general_ci COMMENT '别名或昵称',
  `relationships` text COLLATE utf8mb4_general_ci COMMENT '人际关系列表JSON',
  `profile_summary` text COLLATE utf8mb4_general_ci COMMENT '个人简介',
  `key_attributes` text COLLATE utf8mb4_general_ci COMMENT '关键属性JSON',
  `avatar` varchar(512) COLLATE utf8mb4_general_ci COMMENT '头像URL',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`person_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_canonical_name` (`canonical_name`),
  KEY `idx_is_user` (`is_user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='人员记忆表'
"""

person_memory = Table(
    'person_memory',MetaData(),
    Column('person_id',String(128),primary_key=True,comment='人员唯一标识'),
    Column('user_id', String(255), nullable=True, comment='关联的用户ID'),
    Column('is_user',Boolean,nullable=False,default=False,comment='是否为用户'),
    Column('canonical_name',String(256),nullable=False,comment='正式姓名'),
    Column('aliases',Text,comment='别名或昵称'),
    Column('relationships',Text,comment='人际关系列表JSON'),
    Column('profile_summary',Text,comment='个人简介'),
    Column('key_attributes',Text,comment='关键属性JSON'),
    Column('avatar',String(512),comment='头像URL'),
    Column('created_at',TIMESTAMP,nullable=False,server_default=text('CURRENT_TIMESTAMP'),comment='创建时间'),
    Column('updated_at',TIMESTAMP,nullable=False,server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),comment='更新时间'),
    Index('idx_user_id', 'user_id'),
    Index('idx_canonical_name','canonical_name'),
    Index('idx_is_user','is_user')
)

def insert_person(user_id:str, person_id:str,is_user:bool=False,canonical_name:str="",aliases:str="",relationships:list=[],profile_summary:str="",key_attributes:dict={},avatar:str=""): #添加人员
    ins=person_memory.insert().values(user_id=user_id, person_id=person_id,is_user=is_user,canonical_name=canonical_name,aliases=aliases,relationships=json.dumps(relationships,ensure_ascii=False) if relationships else None,profile_summary=profile_summary,key_attributes=json.dumps(key_attributes,ensure_ascii=False) if key_attributes else None,avatar=avatar)
    try:
        sql_client.insert_return_id(ins)
        logger.info(f"添加人员成功: {person_id} for user: {user_id}")
        return True
    except Exception as e:
        logger.error(f"添加人员失败: {e}")
        return False

def get_all_persons(user_id:str, limit:int=100,offset:int=0): #获取所有人员列表
    stmt=select(person_memory).where(person_memory.c.user_id == user_id).limit(limit).offset(offset)
    try:
        results=sql_client.select_many(stmt)
        persons=[]
        for row in results:
            person=dict(row._mapping)
            if person['relationships']:person['relationships']=json.loads(person['relationships'])
            if person['key_attributes']:person['key_attributes']=json.loads(person['key_attributes'])
            persons.append(person)
        return persons
    except Exception as e:
        logger.error(f"获取人员列表失败: {e}")
        return []

def get_person_by_id(user_id:str, person_id:str, conn=None): #根据ID获取人员详情
    stmt=select(person_memory).where(and_(person_memory.c.person_id==person_id, person_memory.c.user_id==user_id))
    client = conn if conn else sql_client
    try:
        result=client.select_one(stmt)
        if result:
            person=dict(result._mapping)
            # JSON字段已经在服务层处理，这里保持原始数据
            return person
        return None
    except Exception as e:
        logger.error(f"获取人员详情失败: {e}")
        return None

def update_person(user_id:str, person_id:str, conn=None, **kwargs): #更新人员信息
    update_values={}
    for k,v in kwargs.items():
        if v is not None:
            if k in ['relationships','key_attributes'] and isinstance(v,(dict,list)):
                update_values[k]=json.dumps(v,ensure_ascii=False)
            else:
                update_values[k]=v
    if not update_values:return False
    stmt=person_memory.update().where(and_(person_memory.c.person_id==person_id, person_memory.c.user_id==user_id)).values(**update_values)
    client = conn if conn else sql_client
    try:
        result=client.update(stmt)
        logger.info(f"更新人员成功: {person_id} for user: {user_id}")
        return result.rowcount>0
    except Exception as e:
        logger.error(f"更新人员失败: {e}")
        return False

def search_persons_by_name(user_id:str, name:str,limit:int=10): #按姓名、简介、属性等广泛搜索人员
    stmt=select(person_memory).where(
        and_(
            person_memory.c.user_id == user_id,
            or_(
                person_memory.c.canonical_name.like(f'%{name}%'),
                person_memory.c.aliases.like(f'%{name}%'),
                person_memory.c.profile_summary.like(f'%{name}%'),
                person_memory.c.key_attributes.like(f'%{name}%')
            )
        )
    ).order_by(
        (person_memory.c.canonical_name == name).desc(),
        person_memory.c.updated_at.desc()
    ).limit(limit)
    try:
        results=sql_client.select_many(stmt)
        persons=[]
        for row in results:
            person=dict(row._mapping)
            if person['relationships']:person['relationships']=json.loads(person['relationships'])
            if person['key_attributes']:person['key_attributes']=json.loads(person['key_attributes'])
            persons.append(person)
        return persons
    except Exception as e:
        logger.error(f"搜索人员失败: {e}")
        return []

def delete_person(user_id:str, person_id:str, conn=None): #删除人员
    stmt=person_memory.delete().where(and_(person_memory.c.person_id==person_id, person_memory.c.user_id==user_id))
    client = conn if conn else sql_client
    try:
        result=client.update(stmt)
        logger.info(f"删除人员成功: {person_id} for user: {user_id}")
        return result.rowcount>0
    except Exception as e:
        logger.error(f"删除人员失败: {e}")
        return False 