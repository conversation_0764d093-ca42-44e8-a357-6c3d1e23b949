########################################################
# 该文件用于操作 'reminders' 表。
# 严格按照提供的表结构和示例脚本模板创建。
########################################################

from datetime import datetime
from typing import List, Optional, Dict, Any

from my_mysql import sql_client
from sqlalchemy import Table, Column, MetaData, insert, update, select, \
     text, and_, or_, delete, Index
from sqlalchemy.dialects.mysql import BIGINT, VARCHAR, TEXT, DATETIME, JSON, ENUM, TIMESTAMP
from utils.logger import logger

#
# CREATE TABLE `reminders` (
#   `reminder_id` bigint unsigned NOT NULL AUTO_INCREMENT,
#   `user_id` varchar(128) COLLATE utf8mb4_unicode_ci NOT NULL,
#   `subject_person_id` varchar(128) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
#   `reminder_text_template` text COLLATE utf8mb4_unicode_ci,
#   `base_event_date` datetime NOT NULL,
#   `advance_notice_config` json DEFAULT NULL,
#   `recurrence_rule` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
#   `next_trigger_time` datetime NOT NULL,
#   `status` enum('active','processing','completed') COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'active',
#   `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
#   `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
#   PRIMARY KEY (`reminder_id`),
#   KEY `idx_user_id` (`user_id`),
#   KEY `idx_subject_person_id` (`subject_person_id`),
#   KEY `idx_status_and_trigger_time` (`status`,`next_trigger_time`)
# ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
#

# 定义 reminders 表的元数据
reminders_table = Table(
    'reminders', MetaData(),
    Column('reminder_id', BIGINT(unsigned=True), primary_key=True, autoincrement=True),
    Column('user_id', VARCHAR(128), nullable=False),
    Column('subject_person_id', VARCHAR(128), nullable=True),
    Column('reminder_text_template', TEXT, nullable=True),
    Column('base_event_date', DATETIME, nullable=False),
    Column('advance_notice_config', JSON, nullable=True),
    Column('recurrence_rule', VARCHAR(255), nullable=True),
    Column('next_trigger_time', DATETIME, nullable=False),
    Column('status', ENUM('active', 'processing', 'completed'), nullable=False, server_default='active'),
    Column('created_at', TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP')),
    Column('updated_at', TIMESTAMP, nullable=False, server_default=text('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP')),

    # 定义索引
    Index('idx_user_id', 'user_id'),
    Index('idx_subject_person_id', 'subject_person_id'),
    Index('idx_status_and_trigger_time', 'status', 'next_trigger_time')
)


def insert_reminder(
    user_id: str,
    base_event_date: datetime,
    next_trigger_time: datetime,
    subject_person_id: Optional[str] = None,
    reminder_text_template: Optional[str] = None,
    advance_notice_config: Optional[Dict] = None,
    recurrence_rule: Optional[str] = None,
    status: str = 'active'
) -> Optional[int]:
    """
    向 reminders 表中插入一条新记录 (上传数据)。
    成功则返回新记录的 reminder_id，失败则返回 None.
    """
    ins = reminders_table.insert().values(
        user_id=user_id,
        subject_person_id=subject_person_id,
        reminder_text_template=reminder_text_template,
        base_event_date=base_event_date,
        advance_notice_config=advance_notice_config,
        recurrence_rule=recurrence_rule,
        next_trigger_time=next_trigger_time,
        status=status
    )
    try:
        last_id = sql_client.insert_return_id(ins)
        logger.info(f"成功插入提醒记录，ID: {last_id}")
        return last_id
    except Exception as e:
        logger.error(f"插入提醒记录失败: {e}")
        return None

def query_reminder_by_id(reminder_id: int) -> Optional[Dict]:
    """按主键 (reminder_id) 搜索。"""
    stmt = select(reminders_table).where(reminders_table.c.reminder_id == reminder_id)
    try:
        return sql_client.select_one(stmt)
    except Exception as e:
        logger.error(f"按ID查询提醒失败 (ID: {reminder_id}): {e}")
        return None

def query_reminders_by_user(user_id: str, subject_person_id: Optional[str] = None) -> List[Dict]:
    """按索引键 (user_id, subject_person_id) 搜索。"""
    conds = [reminders_table.c.user_id == user_id]
    if subject_person_id:
        conds.append(reminders_table.c.subject_person_id == subject_person_id)
    
    stmt = select(reminders_table).where(and_(*conds))
    try:
        return sql_client.select_many(stmt)
    except Exception as e:
        logger.error(f"按用户查询提醒失败 (UserID: {user_id}): {e}")
        return []

def query_reminders_by_status_and_time(status: str, before_time: datetime) -> List[Dict]:
    """按复合索引键 (status, next_trigger_time) 搜索。"""
    conds = [
        reminders_table.c.status == status,
        reminders_table.c.next_trigger_time <= before_time
    ]
    stmt = select(reminders_table).where(and_(*conds)).order_by(reminders_table.c.next_trigger_time)
    try:
        return sql_client.select_many(stmt)
    except Exception as e:
        logger.error(f"按状态和时间查询提醒失败 (Status: {status}): {e}")
        return []

def update_reminder_status(reminder_id: int, status: str) -> bool:
    """更新指定提醒的状态。"""
    stmt = (
        reminders_table.update()
        .where(reminders_table.c.reminder_id == reminder_id)
        .values(status=status)
    )
    try:
        result = sql_client.update(stmt)
        affected_rows = result.rowcount if result else 0
        if affected_rows > 0:
            logger.info(f"成功更新提醒状态 (ID: {reminder_id}, Status: {status})")
            return True
        else:
            logger.warning(f"未找到要更新的提醒记录或状态未变化 (ID: {reminder_id})")
            return False
    except Exception as e:
        logger.error(f"更新提醒状态失败 (ID: {reminder_id}): {e}")
        return False

def delete_reminder_by_id(reminder_id: int) -> bool:
    """按主键 (reminder_id) 删除记录。"""
    stmt = reminders_table.delete().where(reminders_table.c.reminder_id == reminder_id)
    try:
        result = sql_client.update(stmt)
        affected_rows = result.rowcount if result else 0
        if affected_rows > 0:
            logger.info(f"成功删除提醒记录 (ID: {reminder_id})")
            return True
        else:
            logger.warning(f"未找到要删除的提醒记录 (ID: {reminder_id})")
            return False
    except Exception as e:
        logger.error(f"删除提醒记录失败 (ID: {reminder_id}): {e}")
        return False

# ====================================================================
# 测试函数
# ====================================================================
def test_reminders_operations():
    print("\n==== 1. 测试插入 (上传) ====")
    test_user_id = "test_user_123"
    new_id = insert_reminder(
        user_id=test_user_id,
        base_event_date=datetime(2025, 1, 1),
        next_trigger_time=datetime(2024, 12, 25, 9, 0, 0),
        reminder_text_template="新年提醒"
    )
    if not new_id:
        print("测试中断：插入失败。")
        return

    print(f"成功插入记录，ID: {new_id}")

    print("\n==== 2. 测试搜索 (按主键) ====")
    record = query_reminder_by_id(new_id)
    print(f"查询结果: {dict(record.items()) if record else '未找到'}")
    assert record and record['reminder_id'] == new_id

    print("\n==== 3. 测试搜索 (按索引键 user_id) ====")
    records = query_reminders_by_user(user_id=test_user_id)
    print(f"为用户 '{test_user_id}' 找到 {len(records)} 条记录。")
    assert len(records) > 0

    print("\n==== 4. 测试搜索 (按复合索引键 status, next_trigger_time) ====")
    due_records = query_reminders_by_status_and_time('active', datetime(2024, 12, 31))
    print(f"找到 {len(due_records)} 条 'active' 且在年底前到期的记录。")
    assert len(due_records) > 0

    print("\n==== 5. 测试更新 ====")
    update_success = update_reminder_status(new_id, 'completed')
    print(f"更新操作是否成功: {update_success}")
    updated_record = query_reminder_by_id(new_id)
    print(f"更新后状态: {updated_record['status'] if updated_record else '记录不存在'}")
    assert updated_record and updated_record['status'] == 'completed'

    print("\n==== 6. 测试删除 ====")
    delete_success = delete_reminder_by_id(new_id)
    print(f"删除操作是否成功: {delete_success}")
    final_check = query_reminder_by_id(new_id)
    print(f"删除后再次查询: {'未找到，符合预期' if not final_check else '错误，记录依然存在'}")
    assert not final_check

    print("\n==== 测试完成 ====")

if __name__ == '__main__':
    # 注意: 运行此测试需要您的项目环境已正确配置，
    # 包括 `my_mysql.sql_client` 和 `utils.logger`。
    test_reminders_operations()