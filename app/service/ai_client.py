import requests
import time
import json
from typing import List, Dict, Any, Optional, Union

from utils.logger import logger
from utils.utils import timeit
from configs import lion_config

# API 端点
AIGC_URL = 'https://aigc.sankuai.com/v1/openai/native/chat/completions'
TIKTOKEN_URL = "https://aigc.sankuai.com/v1/openai/tiktoken"
CLAUDE_URL = "https://aigc.sankuai.com/v1/claude/aws/v1/messages"
CLAUDE_STREAM_URL = "https://aigc.sankuai.com/v1/claude/stream/v1/messages"


@timeit
def get_token_num(messages: List[Dict[str, Any]], function_call: Optional[List[Dict[str, Any]]] = None) -> int:
    """
    获取消息的token数量
    
    Args:
        messages: 消息列表
        function_call: 函数调用信息
        
    Returns:
        int: token数量，失败返回-1
    """
    if function_call is None:
        request_data = {
            "model": lion_config.AI_MODEL,
            "messages": messages,
        }
    else:
        request_data = {
            "model": lion_config.AI_MODEL,
            "messages": messages,
            "function_call": function_call
        }

    response = requests.post(TIKTOKEN_URL, json=request_data)
    if response.status_code != 200:
        logger.error(f"获取token数时接口调用失败，响应码:{response.status_code}, 响应信息:{response.text}")
        return -1
    data = response.json().get("data", {})
    return data.get('token_len', -1)

@timeit
def send_to_ai(data: Dict[str, Any] = None, need_logger: bool = True) -> Optional[requests.Response]:
    """
    发送请求到AI模型
    
    Args:
        data: 请求数据，格式如下：
        {
            "model": model,
            "messages": messages,
            "stream": generator is not None,
            "temperature": 0.0,
            "max_tokens": 4000,
        }
        
    Returns:
        requests.Response: 响应对象，失败返回None
    """
    def message_chunk():
        """截断消息以符合token限制"""
        nonlocal data
        messages = data.get('messages', None)
        if messages is not None:
            token_num = get_token_num(messages)
            if token_num > lion_config.TOKEN_CHUNK:
                logger.info(f"token数超过限制，进行截断，当前token总长度:{token_num}")
                before_last_words = sum(len(x['content']) for x in messages[:-1])
                messages[-1]['content'] = messages[-1]['content'][:lion_config.TOKEN_CHUNK-before_last_words]
                logger.info(f"token数超过限制，进行截断，当前token总长度:{token_num}, 截断后长度为: {get_token_num(messages)}")
        data['messages'] = messages

    try:
        text = data.get('messages', None)
        tools = data.get("tools", None)
        model = data["model"]
        if need_logger:
            logger.info(f"传给大模型{model}的信息 {text}")
            logger.info(f"传给大模型{model}的工具 {tools}")
        headers = {
            "Authorization": "Bearer 1838824241643597850",
            "Content-Type": "application/json",
        }
        try:
            # 截断输入，TODO: 最好从token处截断，会更加准确，从文本中截断可能会依然不符合要求，因为存在一个token对应多个字符的情况
            # message_chunk() 
            url = AIGC_URL if model != "anthropic.claude-3.7-sonnet" else (CLAUDE_URL if data.get("stream", False) == False else CLAUDE_STREAM_URL)
            if need_logger:
                logger.info(f"发送请求到{url}, 模型为: [{model}]")
            return requests.post(url, headers=headers, json=data, stream=data.get("stream", False), timeout=600)
        except Exception as e:
            logger.error(f'调用模型返回时失败，失败信息:{str(e)}')
            return None
    except Exception as e:
        logger.error(f'调用模型返回时失败，失败信息:{str(e)}')
        return None

def convert_standard_format_to_claude_format(messages: list[dict['role': str, 'content': str]])\
     -> list[list[dict['role': str, 'content': list[dict["type":str,"text":str]]]], str]:
    def content_check(content) -> list[dict["type":str,"text":str]] | None:
        if isinstance(content, str):
            return {"type": "text", "text": content}
        else:
            logger.error(f"Temporary, our service only supports text input, please modify the input format, input: {content}")
            return None
    ret = []
    tmp_role = None
    tmp_content = []
    system_prompt = None
    for msg in messages:
        role = msg["role"]
        content = msg["content"]
        if role == 'system':
            system_prompt = content
            continue
        if tmp_role == role:
            ck_content = content_check(content)
            if ck_content is not None:
                tmp_content.append(ck_content)
        else: # 角色切换
            if tmp_role != None and len(tmp_content) > 0: # 不是初始情况和特殊情况
                ret.append({"role": tmp_role, "content": tmp_content}) # 添加到输入中
            
            tmp_role = role # 重置角色
            ck_content = content_check(content)
            if ck_content is not None:
                tmp_content = [ck_content] # 重置内容
            
            
    if tmp_role != None and len(tmp_content) > 0:
        ret.append({"role": tmp_role, "content": tmp_content}) # 添加到输入中
    return ret, system_prompt

def stream(generator, data:Dict[str, Any]) -> None:
    response = send_to_ai(data, False)
    for line in response.iter_lines():
        if line and line.startswith(b"data: "):
            line = line.decode('utf-8')
            generator.send(json.dumps(line[6:], ensure_ascii=False))
    generator.close()

