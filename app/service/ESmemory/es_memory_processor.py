import json
from utils.logger import logger
from service.ai_client import send_to_ai
from configs.lion_config import get_value
from service.mysql_person_service import add_person,search_persons_by_name_mysql
from service.ESmemory.es_event_service import add_event

def extract_memory_from_conversation(conversation_text:str,user_id:str): #从对话中提取记忆信息
    try:
        prompt=get_value("humanrelation.memory_extraction_prompt","") #使用Lion配置
        if not prompt:
            logger.error("未配置记忆提取提示词")
            return {"result":"error","reason":"未配置提示词","data":{"persons":[],"events":[]}}
        
        query_input={"model":get_value("humanrelation.memory_extraction_model","gpt-4o-mini"),"messages":[{"role":"system","content":prompt},{"role":"user","content":conversation_text}],"stream":False,"temperature":0.0,"max_tokens":1000}
        
        response=send_to_ai(query_input)
        response_data=json.loads(response.text)
        result=json.loads(response_data["choices"][0]["message"]["content"].strip())
        
        logger.info(f"提取的记忆信息: {result}")
        return {"result":"success","data":result}
    except Exception as e:
        logger.error(f"提取记忆信息失败: {str(e)}")
        return {"result":"error","reason":str(e),"data":{"persons":[],"events":[]}}

def process_and_store_memory(conversation_text:str,user_id:str): #处理并存储记忆(人员用MySQL,事件用ES)
    try:
        extraction_result=extract_memory_from_conversation(conversation_text,user_id)
        if extraction_result["result"]!="success":
            return extraction_result
        
        data=extraction_result["data"]
        stored_persons,stored_events=[],[]
        
        # 处理人员信息 - 使用MySQL
        for person_data in data.get("persons",[]):
            # 检查是否已存在相似人员
            search_result=search_persons_by_name_mysql(person_data.get("canonical_name",""))
            if search_result["result"]=="success" and search_result["persons"]:
                logger.info(f"人员已存在: {person_data.get('canonical_name','')}")
                continue
            
            # 添加新人员到MySQL
            result=add_person(canonical_name=person_data.get("canonical_name",""),aliases=person_data.get("aliases",""),profile_summary=person_data.get("profile_summary",""),key_attributes=person_data.get("key_attributes",{}))
            if result["result"]=="success":
                stored_persons.append(result["person_id"])
        
        # 处理事件信息 - 继续使用ES
        event_index=get_value("humanrelation.event_index_name","memory_event_store")
        for event_data in data.get("events",[]):
            result=add_event(event_index,description_text=event_data.get("description_text",""),participants=event_data.get("participants",[]),location=event_data.get("location",""),topics=event_data.get("topics",[]),sentiment=event_data.get("sentiment",""))
            if result["result"]=="success":
                stored_events.append(result["event_id"])
        
        return {"result":"success","stored_persons":stored_persons,"stored_events":stored_events}
    except Exception as e:
        logger.error(f"处理存储记忆失败: {str(e)}")
        return {"result":"error","reason":str(e)}

def retrieve_relevant_memory(query_text:str,max_results:int=10): #检索相关记忆(人员用MySQL,事件用ES)
    try:
        from service.ESmemory.es_event_service import search_events_by_text
        
        # 搜索相关事件 - 使用ES
        event_index=get_value("humanrelation.event_index_name","memory_event_store")
        events_result=search_events_by_text(event_index,query_text,max_results)
        events=events_result.get("events",[]) if events_result["result"]=="success" else []
        
        # 搜索相关人员 - 使用MySQL
        persons_result=search_persons_by_name_mysql(query_text,max_results)
        persons=persons_result.get("persons",[]) if persons_result["result"]=="success" else []
        
        return {"result":"success","events":events,"persons":persons}
    except Exception as e:
        logger.error(f"检索相关记忆失败: {str(e)}")
        return {"result":"error","reason":str(e),"events":[],"persons":[]} 