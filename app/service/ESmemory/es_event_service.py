from datetime import datetime
from uuid import uuid4

from service.ESmemory.es_memory_client import client
from utils.logger import logger


def add_event(
    index_name: str,
    user_id: str,
    description_text: str = "",
    participants: list = [],
    location: str = "",
    topics: list = [],
    sentiment: str = "",
    **kwargs,
):  # 添加事件
    event_id = str(uuid4())
    document = {
        "event_id": event_id,
        "user_id": user_id,
        "description_text": description_text,
        "timestamp": datetime.now(),
        "participants": participants,
        "location": location,
        "topics": topics,
        "sentiment": sentiment,
    }
    try:
        # 使用OpenSearch的index方法
        client.index(index=index_name, id=event_id, body=document)
        logger.info(f"添加事件成功: {event_id}, 内容: {document}")
        return {"result": "success", "event_id": event_id}
    except Exception as e:
        logger.error(f"添加事件到ES失败: {e}")
        return {"result": "error", "reason": str(e)}


def search_events_by_text(index_name: str, user_id: str, query_text: str, max_results: int = 5):  # 按文本搜索事件
    """使用 bool 查询结合 simple_query_string 和 filter 实现用户隔离的模糊搜索"""
    try:
        query = {
            "bool": {
                "must": {
                    "simple_query_string": {
                        "query": query_text,
                        "fields": ["description_text", "participants", "location", "topics"],
                        "default_operator": "AND",
                    }
                },
                "filter": [{"term": {"user_id": user_id}}],
            }
        }
        logger.info(f"ES事件搜索查询: {query}")
        response = client.search(index=index_name, body={"query": query, "size": max_results})

        hits = response.get("hits", {}).get("hits", [])
        # logger.info(f"ES事件搜索原始结果: {hits}") # 日志太长，暂时关闭

        events = []
        for hit in hits:
            event_data = hit.get("_source", {})
            event_data["score"] = hit.get("_score")
            events.append(event_data)

        return {"result": "success", "events": events}
    except Exception as e:
        logger.error(f"从ES搜索事件失败: {e}")
        return {"result": "error", "reason": str(e), "events": []}


def search_events_by_participant(
    index_name: str, user_id: str, participant_id: str, size: int = 10
):  # 按参与者搜索事件
    try:
        search_body = {
            "size": size,
            "query": {"bool": {"filter": [{"term": {"user_id": user_id}}, {"term": {"participants": participant_id}}]}},
        }
        response = client.search(index=index_name, body=search_body)
        events = [hit["_source"] for hit in response["hits"]["hits"]]
        return {"result": "success", "events": events}
    except Exception as e:
        logger.error(f"按参与者搜索事件失败: {str(e)}")
        return {"result": "error", "reason": str(e), "events": []}


def get_recent_events(index_name: str, user_id: str, size: int = 50):  # 获取最近的事件
    """获取指定用户最近的事件，按时间戳降序排序"""
    try:
        search_body = {
            "size": size,
            "sort": [{"timestamp": {"order": "desc"}}],
            "query": {"term": {"user_id": user_id}},
        }
        response = client.search(index=index_name, body=search_body)
        events = [hit["_source"] for hit in response["hits"]["hits"]]
        return {"result": "success", "events": events}
    except Exception as e:
        logger.error(f"获取最近事件失败: {str(e)}")
        return {"result": "error", "reason": str(e), "events": []}


def get_event_by_id(index_name: str, user_id: str, event_id: str):  # 根据ID获取事件详情
    try:
        query = {"bool": {"filter": [{"term": {"user_id": user_id}}, {"term": {"event_id": event_id}}]}}
        response = client.search(index=index_name, body={"query": query, "size": 1})
        hits = response.get("hits", {}).get("hits", [])
        if hits:
            return {"result": "success", "event": hits[0]["_source"]}
        return {"result": "error", "reason": "事件不存在或无权限访问", "event": None}
    except Exception as e:
        logger.error(f"获取事件详情失败: {str(e)}")
        return {"result": "error", "reason": str(e), "event": None}


def update_participant_in_events(
    index_name: str, user_id: str, old_participant_id: str, new_participant_id: str
):  # 更新事件中的参与者ID
    """将一个参与者ID更新为另一个，用于档案合并，确保在用户范围内操作"""
    try:
        script = {
            "source": "if(ctx._source.participants.indexOf(params.old_id)!=-1){ctx._source.participants.remove(ctx._source.participants.indexOf(params.old_id));if(ctx._source.participants.indexOf(params.new_id)==-1){ctx._source.participants.add(params.new_id)}}",
            "lang": "painless",
            "params": {"old_id": old_participant_id, "new_id": new_participant_id},
        }
        query = {"bool": {"filter": [{"term": {"user_id": user_id}}, {"term": {"participants": old_participant_id}}]}}
        response = client.update_by_query(index=index_name, body={"script": script, "query": query})
        logger.info(f"更新事件参与者成功: {old_participant_id} -> {new_participant_id}, 响应: {response}")
        return {"result": "success", "updated_count": response.get("updated")}
    except Exception as e:
        logger.error(f"更新事件参与者失败: {str(e)}")
        return {"result": "error", "reason": str(e)}


def update_event_content(index_name: str, user_id: str, event_id: str, **kwargs):
    """更新事件内容，支持更新描述、参与者、地点、主题、情感等字段"""
    try:
        # 构建更新文档
        update_doc = {}
        allowed_fields = ["description_text", "participants", "location", "topics", "sentiment"]

        for field, value in kwargs.items():
            if field in allowed_fields:
                update_doc[field] = value

        if not update_doc:
            return {"result": "error", "reason": "没有提供有效的更新字段"}

        # 添加更新时间戳
        update_doc["updated_at"] = datetime.now()

        # 构建更新请求
        update_body = {"doc": update_doc, "doc_as_upsert": False}  # 如果文档不存在，不创建新文档

        # 先检查事件是否存在且属于该用户
        check_result = get_event_by_id(index_name, user_id, event_id)
        if check_result.get("result") != "success":
            return {"result": "error", "reason": "事件不存在或无权限访问"}

        # 执行更新
        client.update(index=index_name, id=event_id, body=update_body)

        logger.info(f"更新事件成功: {event_id}, 更新字段: {list(update_doc.keys())}")
        return {"result": "success", "event_id": event_id, "updated_fields": list(update_doc.keys())}

    except Exception as e:
        logger.error(f"更新事件内容失败: {str(e)}")
        return {"result": "error", "reason": str(e)}
