########################################################
# 增强记忆管理服务 - 实现"以人物为中心"的完整记忆机制
########################################################

import json
from datetime import datetime,timedelta
from typing import List,Dict,Optional
from utils.logger import logger
from service.ai_client import send_to_ai
from configs.lion_config import get_value
from service.mysql_person_service import add_person, get_all_persons_mysql,search_persons_by_name_mysql,get_person_by_id_mysql,update_person_mysql,delete_person_mysql
from service.ESmemory.es_event_service import add_event,search_events_by_text,get_recent_events,update_participant_in_events
from uuid import uuid4
from my_mysql.entity.person_table import get_person_by_id, update_person, delete_person
from sqlalchemy import and_
import re
import threading
import time

class EnhancedMemoryService:
    def __init__(self):
        self.event_index=get_value("humanrelation.event_index_name","memory_event_store")
    
    def extract_and_process_memory(self,conversation_text:str,user_id:str): #步骤一：输入处理与实体识别
        """从对话中提取记忆，并根据情况返回结构化指令"""
        try:
            extraction_result=self._extract_memory_from_conversation(conversation_text,user_id)
            if extraction_result.get("result")!="success": return extraction_result
            
            data=extraction_result.get("data",{})
            intent=data.get("intent","add_memory")
            
            # 处理不同类型的意图
            if intent=="merge_persons" and len(data.get("persons",[]))==2:
                merge_result = self._handle_auto_merge_intent(data["persons"],user_id)
                if merge_result:
                    return merge_result
                # 如果 merge_result 为 None，则表示无法合并，将按默认流程处理

            elif intent=="alias_relation":
                return self._handle_alias_relation(data,user_id)
            elif intent=="rename_relation":
                return self._handle_rename_relation(data,user_id)
            
            # 正常的添加记忆流程 (也是合并失败时的后备流程)
            processed_persons,processed_events=[],[]
            for person_data in data.get("persons",[]):
                # --- 时间表达式转换 ---
                if person_data.get("profile_summary"):
                    person_data["profile_summary"] = self._replace_relative_time(person_data["profile_summary"])
                if person_data.get("key_attributes"):
                    for k, v in person_data["key_attributes"].items():
                        if isinstance(v, str):
                            person_data["key_attributes"][k] = self._replace_relative_time(v)
                # --- 结束 ---
                processed_persons.append(self._process_person_with_temporal_info(person_data,conversation_text,user_id))
            
            for event_data in data.get("events",[]):
                # --- 时间表达式转换 ---
                if event_data.get("description_text"):
                    event_data["description_text"] = self._replace_relative_time(event_data["description_text"])
                # --- 结束 ---
                processed_events.append(self._process_event(event_data,user_id))
            
            validations = [p.get("verification_message") for p in processed_persons if p.get("verification_message")]
            return {"action_code":"PROCESS_COMPLETE","payload":{"persons":processed_persons,"events":processed_events,"validations":validations}}
        except Exception as e:
            logger.error(f"提取处理记忆失败: {str(e)}")
            return {"action_code":"ERROR","payload":{"reason":str(e)}}
    
    def _handle_auto_merge_intent(self, persons_data: list,user_id:str):
        """处理自动合并意图，如果成功则返回结果，如果失败则返回None以进行后备处理"""
        try:
            name_a, name_b = persons_data[0].get("canonical_name"), persons_data[1].get("canonical_name")
            res_a = search_persons_by_name_mysql(user_id=user_id,name=name_a, limit=1)
            res_b = search_persons_by_name_mysql(user_id=user_id,name=name_b, limit=1)
            
            if res_a.get("persons") and res_b.get("persons"):
                person_a, person_b = res_a["persons"][0], res_b["persons"][0]
                
                # 规则：保留更新时间更近的档案作为主档案
                primary, secondary = (person_a, person_b) if person_a["updated_at"] >= person_b["updated_at"] else (person_b, person_a)

                merge_result = self.execute_merge_persons(user_id,primary["person_id"], secondary["person_id"])
                
                if merge_result.get("result") == "success":
                    message = f"自动合并档案成功：已将'{secondary['canonical_name']}'的信息合并入'{primary['canonical_name']}'。"
                    logger.info(message)
                    return {"action_code": "MERGE_EXECUTED", "payload": {"message": message}}
                else:
                    return {"action_code": "ERROR", "payload": {"reason": merge_result.get("reason", "合并失败")}}
            else:
                logger.warning(f"无法找到用于合并的档案({name_a}, {name_b})，将按普通记忆添加流程处理。")
                return None # 表示无法合并，应执行后备流程
                
        except Exception as e:
            logger.error(f"处理自动合并意图失败: {e}")
            return None # 表示无法合并，应执行后备流程
    
    def execute_merge_persons(self,user_id:str,primary_person_id:str,secondary_person_id:str):
        """执行档案合并，使用手动事务管理"""
        conn = None
        try:
            # 安全检查：不能自己合并自己
            if primary_person_id == secondary_person_id:
                logger.info("无需合并，两个ID指向同一个人。")
                return {"result": "success", "message": "无需合并，指向同一个人。"}

            # 手动管理连接和事务
            from my_mysql.sql_client import CLIENT
            from my_mysql.entity.person_table import person_memory
            from sqlalchemy import update as sql_update, delete as sql_delete, select
            
            conn = CLIENT.connect()
            trans = conn.begin()  # 开始事务
            
            # 1. 获取两份档案 - 直接在事务中查询，并带上user_id
            select_stmt_a = select(person_memory).where(and_(person_memory.c.person_id==primary_person_id, person_memory.c.user_id==user_id))
            select_stmt_b = select(person_memory).where(and_(person_memory.c.person_id==secondary_person_id, person_memory.c.user_id==user_id))
            
            person_a_res = conn.execute(select_stmt_a).fetchone()
            person_b_res = conn.execute(select_stmt_b).fetchone()
            
            if not person_a_res or not person_b_res: 
                raise Exception("无法找到档案")
            
            person_a = dict(person_a_res._mapping)
            person_b = dict(person_b_res._mapping)

            # 2. 合并信息 - 安全处理所有JSON字段
            try:
                aliases_a=json.loads(person_a.get("aliases") or '[]')
                if not isinstance(aliases_a, list): aliases_a = []
            except:
                aliases_a = []
            
            try:
                aliases_b=json.loads(person_b.get("aliases") or '[]')
                if not isinstance(aliases_b, list): aliases_b = []
            except:
                aliases_b = []
            
            merged_aliases=list(set(aliases_a+[person_b["canonical_name"]]+aliases_b))
            
            try:
                attrs_a=json.loads(person_a.get("key_attributes") or '{}')
                if not isinstance(attrs_a, dict): attrs_a = {}
            except:
                attrs_a = {}
            
            try:
                attrs_b=json.loads(person_b.get("key_attributes") or '{}')
                if not isinstance(attrs_b, dict): attrs_b = {}
            except:
                attrs_b = {}
            
            merged_attributes={**attrs_b,**attrs_a}
            
            # 处理datetime对象，确保JSON序列化成功
            def convert_datetime_to_string(obj):
                """递归转换字典中的datetime对象为字符串"""
                if isinstance(obj, dict):
                    return {k: convert_datetime_to_string(v) for k, v in obj.items()}
                elif isinstance(obj, list):
                    return [convert_datetime_to_string(item) for item in obj]
                elif isinstance(obj, datetime):
                    return obj.isoformat()
                else:
                    return obj
            
            # 清理合并后的数据，确保可以JSON序列化
            merged_attributes = convert_datetime_to_string(merged_attributes)
            
            # 安全处理relationships字段
            try:
                rels_a=json.loads(person_a.get("relationships") or '[]')
                if not isinstance(rels_a, list): rels_a = []
            except:
                rels_a = []
            
            try:
                rels_b=json.loads(person_b.get("relationships") or '[]')
                if not isinstance(rels_b, list): rels_b = []
            except:
                rels_b = []
            
            # 合并关系，确保每个元素都是字典
            merged_rels = []
            all_rels = rels_a + rels_b
            seen_ids = set()
            
            for rel in all_rels:
                if isinstance(rel, dict):
                    rel_id = rel.get('person_id', str(rel))
                    if rel_id not in seen_ids:
                        merged_rels.append(rel)
                        seen_ids.add(rel_id)
                elif isinstance(rel, str) and rel not in seen_ids:
                    # 如果是字符串，转换为简单的关系对象
                    merged_rels.append({"person_id": rel, "relation": "unknown"})
                    seen_ids.add(rel)
            
            # 最终清理，确保所有数据都可以JSON序列化
            merged_rels = convert_datetime_to_string(merged_rels)
            
            # 3. LLM生成新小传
            new_summary=self._summarize_merged_profile(person_a,person_b)

            # 4. 更新主档案
            update_stmt=sql_update(person_memory).where(person_memory.c.person_id==primary_person_id).values(
                aliases=json.dumps(merged_aliases,ensure_ascii=False),
                key_attributes=json.dumps(merged_attributes,ensure_ascii=False),
                relationships=json.dumps(merged_rels,ensure_ascii=False),
                profile_summary=new_summary
            )
            conn.execute(update_stmt)
            
            # 5. 更新事件记录
            update_participant_in_events(self.event_index,user_id,secondary_person_id,primary_person_id)

            # 6. 删除次要档案
            delete_stmt=sql_delete(person_memory).where(person_memory.c.person_id==secondary_person_id)
            conn.execute(delete_stmt)
            
            # 7. 提交事务
            trans.commit()
            logger.info(f"档案合并成功: {secondary_person_id} -> {primary_person_id}")
            return {"result":"success"}
        except Exception as e:
            if conn:
                try:
                    trans.rollback()
                except:
                    pass
            logger.error(f"档案合并失败: {e}")
            return {"result":"error", "reason": str(e)}
        finally:
            if conn:
                conn.close()
    
    def _summarize_merged_profile(self,person_a:dict,person_b:dict):
        """调用LLM为合并后的档案生成新的小传"""
        
        # 处理datetime对象，确保JSON序列化成功
        def convert_datetime_to_string(obj):
            """递归转换字典中的datetime对象为字符串"""
            if isinstance(obj, dict):
                return {k: convert_datetime_to_string(v) for k, v in obj.items()}
            elif isinstance(obj, list):
                return [convert_datetime_to_string(item) for item in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            else:
                return obj
        
        # 清理输入数据，确保可以JSON序列化
        clean_person_a = convert_datetime_to_string(person_a)
        clean_person_b = convert_datetime_to_string(person_b)
        
        prompt=get_value("humanrelation.profile_merge_summarization_prompt","请整合以下两份关于同一个人的资料，生成一段简洁、全面的新个人简介。")
        context=f"档案A: {json.dumps(clean_person_a,ensure_ascii=False)}\n\n档案B: {json.dumps(clean_person_b,ensure_ascii=False)}"
        
        query_input={"model":get_value("humanrelation.memory_extraction_model","gpt-4o-mini"),"messages":[{"role":"system","content":prompt},{"role":"user","content":context}],"stream":False,"temperature":0.5,"max_tokens":800}
        response=send_to_ai(query_input)
        response_data=json.loads(response.text)
        return response_data["choices"][0]["message"]["content"].strip()
    
    def _extract_memory_from_conversation(self,conversation_text:str,user_id:str): #LLM结构化提取
        """从对话历史中提取实体和事件，形成结构化记忆"""
        try:
            prompt=get_value("humanrelation.memory_extraction_prompt","")
            if not prompt:
                logger.error("未配置记忆提取提示词")
                return {"result":"error","reason":"未配置提示词","data":{"persons":[],"events":[]}}
            
            query_input={"model":get_value("humanrelation.memory_extraction_model","gpt-4o-mini"),"messages":[{"role":"system","content":prompt},{"role":"user","content":conversation_text}],"stream":False,"temperature":0.0,"max_tokens":1000}
            
            response=send_to_ai(query_input)
            if not response:
                logger.error("AI服务调用失败，返回None")
                return {"result":"error","reason":"AI服务调用失败","data":{"persons":[],"events":[]}}
            
            logger.info(f"AI服务原始响应: {response.text}")
            
            try:
                response_data=json.loads(response.text)
            except json.JSONDecodeError as e:
                logger.error(f"解析AI服务响应失败: {e}, 原始响应: {response.text}")
                return {"result":"error","reason":"AI服务响应格式错误","data":{"persons":[],"events":[]}}
            
            if "choices" not in response_data or not response_data["choices"]:
                logger.error(f"AI服务响应缺少choices字段: {response_data}")
                return {"result":"error","reason":"AI服务响应格式异常","data":{"persons":[],"events":[]}}
            
            content=response_data["choices"][0]["message"]["content"].strip()
            logger.info(f"LLM返回的内容: {content}")
            
            if not content:
                logger.warning("LLM返回空内容")
                return {"result":"success","data":{"intent":"add_memory","persons":[],"events":[]}}
            
            # 清理Markdown代码块标记
            cleaned_content = content
            if content.startswith("```json"):
                cleaned_content = content[7:]  # 移除开头的```json
            elif content.startswith("```"):
                cleaned_content = content[3:]   # 移除开头的```
            
            if cleaned_content.endswith("```"):
                cleaned_content = cleaned_content[:-3]  # 移除结尾的```
            
            cleaned_content = cleaned_content.strip()
            logger.info(f"清理后的内容: {cleaned_content}")
            
            try:
                result=json.loads(cleaned_content)
                logger.info(f"成功解析的记忆信息: {result}")
                return {"result":"success","data":result}
            except json.JSONDecodeError as e:
                logger.error(f"解析LLM返回的JSON失败: {e}, 清理后内容: {cleaned_content}")
                # 尝试提取可能的JSON部分
                import re
                json_match=re.search(r'\{.*\}',cleaned_content,re.DOTALL)
                if json_match:
                    try:
                        result=json.loads(json_match.group())
                        logger.info(f"从混合内容中提取到JSON: {result}")
                        return {"result":"success","data":result}
                    except:
                        pass
            
            # 如果完全无法解析，返回默认结构
            logger.warning("无法解析LLM输出，返回默认结构")
            return {"result":"success","data":{"intent":"add_memory","persons":[],"events":[]}}
            
        except Exception as e:
            logger.error(f"提取记忆信息失败: {str(e)}")
            return {"result":"error","reason":str(e),"data":{"persons":[],"events":[]}}
    
    def _process_person_with_temporal_info(self,person_data:dict,context:str,user_id:str): #处理人物信息并进行时效性计算
        """处理单个实体，自动创建或更新"""
        canonical_name=person_data.get("canonical_name","")
        if not canonical_name: return {"result":"error","reason":"人物姓名为空"}

        search_result=search_persons_by_name_mysql(user_id=user_id,name=canonical_name, limit=1) # 精确查找一个
        
        # --- 实体消歧 START ---
        if search_result.get("persons"):
            try:
                existing = search_result["persons"][0]
                existing_attrs = json.loads(existing.get("key_attributes") or "{}")
                if not isinstance(existing_attrs, dict): existing_attrs = {}

                new_attrs = person_data.get("key_attributes", {})

                # 核心规则：如果"关系"属性存在且明确冲突，则判定为新人，强制创建新档案
                if "关系" in existing_attrs and "关系" in new_attrs and existing_attrs["关系"] and new_attrs["关系"] and existing_attrs["关系"] != new_attrs["关系"]:
                    logger.info(f"实体消歧：检测到'{canonical_name}'的'关系'属性冲突，强制创建新档案。已有关系: '{existing_attrs['关系']}', 新关系: '{new_attrs['关系']}'")
                    search_result = {"persons": []} # 清空查找结果，以触发后续的"创建"逻辑
            except Exception as e:
                logger.error(f"实体消歧逻辑执行失败: {e}")
        # --- 实体消歧 END ---
        
        processed_attributes=self._process_temporal_attributes(person_data.get("key_attributes",{}))
        verification_message=processed_attributes.pop("verification_message","")

        if search_result.get("persons"): # 更新流程，合并而非覆盖
            existing=search_result["persons"][0]
            person_id=existing["person_id"]

            # 1. 合并别名
            try:
                existing_aliases=json.loads(existing.get("aliases") or "[]")
                if not isinstance(existing_aliases,list): existing_aliases=[]
            except:
                existing_aliases=[]
            new_aliases=person_data.get("aliases",[])
            merged_aliases=list(dict.fromkeys(existing_aliases+new_aliases))

            # 2. 合并关键属性
            try:
                existing_attrs=json.loads(existing.get("key_attributes") or "{}")
                if not isinstance(existing_attrs,dict): existing_attrs={}
            except:
                existing_attrs={}
            merged_attrs={**existing_attrs,**processed_attributes}

            # 3. 构建更新载荷
            update_payload = {}
            
            # 3a. 别名
            if merged_aliases != existing_aliases:
                update_payload["aliases"] = json.dumps(merged_aliases, ensure_ascii=False)

            # 3b. 关键属性
            if merged_attrs != existing_attrs:
                update_payload["key_attributes"] = json.dumps(merged_attrs, ensure_ascii=False)
            
            # 3c. profile_summary 处理逻辑：追加而非覆盖
            new_summary = person_data.get("profile_summary", "").strip()
            existing_summary = existing.get("profile_summary", "") or ""
            
            updated_summary = existing_summary
            if new_summary and new_summary not in existing_summary:
                updated_summary = f"{existing_summary}；{new_summary}" if existing_summary else new_summary

            if updated_summary != existing_summary:
                update_payload["profile_summary"] = updated_summary

            if update_payload: # 只有有变动才更新
                update_person_mysql(user_id=user_id, person_id=person_id, **update_payload)
            action_result={"action":"updated","person_id":person_id}
        else: # 创建新档案
            update_payload = {
                "aliases": json.dumps(person_data.get("aliases", []), ensure_ascii=False),
                "profile_summary": person_data.get("profile_summary",""),
                "key_attributes": json.dumps(processed_attributes, ensure_ascii=False)
            }
            create_result=add_person(user_id=user_id, canonical_name=canonical_name, **update_payload)
            action_result={"action":"created","person_id":create_result.get("person_id")}

            # 新增逻辑：检查新建档案的名称是否像昵称，如果是，则生成追问全名的消息
            if re.match(r"^(老|小|阿).*", canonical_name) or len(canonical_name) <= 2:
                ask_for_name_msg = f"已为您创建了'{canonical_name}'的档案。如果方便的话，可以告诉我TA的正式姓名吗？这样我能记得更牢。"
                if verification_message:
                    verification_message += f"；{ask_for_name_msg}"
                else:
                    verification_message = ask_for_name_msg

        return {"result":"success", "data":action_result, "verification_message": verification_message}
    
    def _process_temporal_attributes(self,attributes:dict): #时效性信息处理
        """处理可计算的时效性信息，如年龄转换为出生年份"""
        processed=attributes.copy()
        verification_messages=[]
        current_year=datetime.now().year
        
        # 处理年龄信息
        if "年龄" in attributes and attributes["年龄"]:
            try:
                age=int(attributes["年龄"])
                birth_year=current_year-age
                processed["出生年份"]=birth_year
                del processed["年龄"]  # 删除会变化的年龄，保存不变的出生年份
                verification_messages.append(f"根据年龄{age}岁计算，出生年份应该是{birth_year}年左右，对吗？")
            except:
                pass
        
        # 处理孩子年龄信息
        if "孩子年龄" in attributes and attributes["孩子年龄"]:
            try:
                child_age=int(attributes["孩子年龄"])
                child_birth_year=current_year-child_age
                processed["孩子出生年份"]=child_birth_year
                del processed["孩子年龄"]
                verification_messages.append(f"孩子{child_age}岁，出生年份应该是{child_birth_year}年左右，对吗？如果能告诉我月份，以后提醒生日就更准了！")
            except:
                pass
        
        # 添加时间戳用于状态信息的定期回顾
        for key in ["职位","公司","学校","年级"]:
            if key in processed:
                processed[f"{key}_更新时间"]=datetime.now().isoformat()
        
        if verification_messages:
            processed["verification_message"]="; ".join(verification_messages)
        
        return processed
    
    def _process_event(self,event_data:dict,user_id:str): #处理事件信息（短期记忆）
        """处理单个事件信息并存入ES"""
        # 提取参与者信息
        participant_names = event_data.get("participants", [])

        # 将参与者名称转换为 person_id
        participant_ids = []
        if participant_names:
            for participant_name in participant_names:
                # 搜索人物档案获取 person_id
                search_result = search_persons_by_name_mysql(user_id=user_id, name=participant_name, limit=1)
                if search_result.get("result") == "success" and search_result.get("persons"):
                    person = search_result["persons"][0]
                    person_id = person["person_id"]
                    participant_ids.append(person_id)
                else:
                    # 如果找不到对应的人物档案，保留原始名称
                    participant_ids.append(participant_name)

        result = add_event(
            index_name=self.event_index,
            user_id=user_id,
            description_text=event_data.get("description_text",""),
            participants=participant_ids,  # 使用转换后的 person_id 列表
            location=event_data.get("location",""),
            topics=event_data.get("topics",[]),
            sentiment=event_data.get("sentiment","")
        )

        if result.get("result") == "success":
            return {"status": "created", "event_id": result.get("event_id"), "data": event_data}
        else:
            return {"status": "error", "reason": result.get("reason"), "data": event_data}
    
    def retrieve_memory_for_conversation(self,query_text:str,user_id:str,max_results:int=5): #步骤二：回忆与建议生成 (智能检索版)
        """为对话检索相关记忆并生成建议"""
        try:
            # 1. 智能提取人名
            person_names = self._extract_names_from_query(query_text, user_id)
            
            persons = []
            events = []
            
            # 2. 根据是否提取到人名，采用不同策略
            if person_names:
                # 策略一：围绕提取到的人名进行精确查找
                logger.info(f"提取到人名: {person_names}，执行精确查找...")
                # 2a. 人物档案检索 (长期记忆)
                for name in person_names:
                    persons_result=search_persons_by_name_mysql(user_id=user_id, name=name, limit=1)
                    if persons_result.get("persons"):
                        persons.extend(persons_result["persons"])
                
                # 2b. 事件记忆检索（短期记忆），也基于人名
                all_event_ids = set()
                for name in person_names:
                    # 使用人名进行语义搜索，找到相关事件
                    events_result=search_events_by_text(index_name=self.event_index, user_id=user_id, query_text=name, max_results=max_results)
                    if events_result.get("result")=="success" and events_result.get("events"):
                        for event in events_result["events"]:
                            # 使用set去重
                            if event.get('event_id') not in all_event_ids:
                                events.append(event)
                                all_event_ids.add(event.get('event_id'))

            else:
                # 策略二：未提取到人名，对整个问题进行语义搜索
                logger.info("未提取到人名，对整个问题进行语义搜索...")
                events_result=search_events_by_text(index_name=self.event_index, user_id=user_id, query_text=query_text, max_results=max_results)
                if events_result.get("result")=="success":
                    events=events_result.get("events",[])
            
            # 3. 智能整合与生成建议
            if persons or events:
                # 去重
                persons = list({p['person_id']: p for p in persons}.values())
                
                suggestions=self._generate_conversation_suggestions(persons,events,query_text)
                return {"result":"success","persons":persons,"events":events,"suggestions":suggestions}
            else:
                return {"result":"success","persons":[],"events":[],"suggestions":""}
        except Exception as e:
            logger.error(f"检索记忆失败: {str(e)}")
            return {"result":"error","reason":str(e)}

    def _extract_names_from_query(self, query_text: str, user_id: str) -> List[str]:
        """使用LLM从查询中提取人名，若失败则使用正则+SQL兜底"""
        try:
            prompt = get_value("humanrelation.retrieval_entity_extraction_prompt", "Extract person names. Return JSON list.")
            
            query_input={"model":get_value("humanrelation.memory_extraction_model","gpt-4o-mini"),"messages":[{"role":"system","content":prompt},{"role":"user","content":query_text}],"stream":False,"temperature":0.0,"max_tokens":100}
            
            response=send_to_ai(query_input)
            names: List[str] = []
            if response:
                response_text = response.text
                if response_text.startswith("```json"):
                    response_text = response_text[7:-3].strip()
                try:
                    parsed=json.loads(response_text)
                    if isinstance(parsed,list):
                        names=[n for n in parsed if isinstance(n,str)]
                except Exception as e:
                    logger.warning(f"LLM人名解析失败:{e}, 原始响应:{response_text}")
            
            # ---------- Fallback: 新的、更智能的兜底策略 ----------
            if not names:
                # 新的后备策略：从数据库加载已知人名进行匹配，而不是用正则猜测
                logger.info("LLM提取人名失败，启用数据库已知人名匹配策略...")
                try:
                    from my_mysql.sql_client import CLIENT
                    from my_mysql.entity.person_table import person_memory
                    from sqlalchemy import select
                    
                    conn = CLIENT.connect()
                    try:
                        # 1. 从数据库获取当前用户的所有人物的姓名和别名
                        select_stmt = select(person_memory.c.canonical_name, person_memory.c.aliases).where(person_memory.c.user_id == user_id)
                        all_persons_res = conn.execute(select_stmt).fetchall()
                        
                        known_names = set()
                        for person in all_persons_res:
                            known_names.add(person.canonical_name)
                            if person.aliases:
                                try:
                                    aliases = json.loads(person.aliases)
                                    if isinstance(aliases, list):
                                        for alias in aliases:
                                            known_names.add(alias)
                                except (json.JSONDecodeError, TypeError):
                                    pass # 忽略无法解析的别名
                        
                        # 2. 检查查询文本中是否包含任何已知人名
                        for name in known_names:
                            if name and name in query_text: # 确保名字不为空
                                names.append(name)
                    finally:
                        conn.close()

                except Exception as db_e:
                    logger.error(f"数据库人名匹配后备方案失败: {db_e}")

            # 去重
            names=list(dict.fromkeys(names))
            logger.info(f"最终提取人名: {names}")
            return names
        except Exception as e:
            logger.error(f"从查询中提取人名失败: {e}")
            return []
    
    def _generate_conversation_suggestions(self,persons:List[dict],events:List[dict],query_context:str): #生成对话建议
        try:
            prompt=get_value("humanrelation.memory_generation_prompt","")
            if not prompt:
                return ""
            
            # 递归把 datetime 转字符串
            def dt2str(obj):
                from datetime import datetime
                if isinstance(obj, dict):
                    return {k: dt2str(v) for k,v in obj.items()}
                if isinstance(obj, list):
                    return [dt2str(v) for v in obj]
                if isinstance(obj, datetime):
                    return obj.isoformat()
                return obj
            
            # 清理事件数据，移除不必要的 vector 字段
            cleaned_events = []
            for event in events:
                clean_event = event.copy()
                clean_event.pop('vector', None) # 安全地移除 vector
                cleaned_events.append(clean_event)
            
            context_data={"persons":dt2str(persons),"events":dt2str(cleaned_events),"query_context":query_context}
            
            query_input={"model":get_value("humanrelation.memory_extraction_model","gpt-4o-mini"),"messages":[{"role":"system","content":prompt},{"role":"user","content":json.dumps(context_data,ensure_ascii=False)}],"stream":False,"temperature":0.7,"max_tokens":500}
            
            response=send_to_ai(query_input)
            response_data=json.loads(response.text)
            suggestions=response_data["choices"][0]["message"]["content"].strip()
            
            return suggestions
        except Exception as e:
            logger.error(f"生成对话建议失败: {str(e)}")
            return ""
    
    def _get_all_user_ids(self) -> List[str]:
        """获取所有唯一的用户ID"""
        from my_mysql.sql_client import CLIENT
        from my_mysql.entity.person_table import person_memory
        from sqlalchemy import distinct, select
        
        conn = None
        try:
            conn = CLIENT.connect()
            stmt = select(distinct(person_memory.c.user_id))
            results = conn.execute(stmt).fetchall()
            return [row[0] for row in results]
        except Exception as e:
            logger.error(f"获取所有用户ID失败: {e}")
            return []
        finally:
            if conn:
                conn.close()

    def _long_term_memory_worker_loop(self):
        """后台工作线程循环，定期执行长期记忆更新任务。"""
        INITIAL_DELAY = 5 * 60  # 5分钟
        WEEKLY_INTERVAL = 7 * 24 * 60 * 60  # 7天
        
        logger.info(f"后台长期记忆更新工作线程已启动。首次任务将在 {INITIAL_DELAY // 60} 分钟后执行。")

        # 首次启动时的初始延迟
        if self._stop_event.wait(INITIAL_DELAY):
            logger.info("后台长期记忆更新工作线程在初始等待期间被停止。")
            return # 线程直接退出

        while not self._stop_event.is_set():
            try:
                logger.info("开始执行定期长期记忆更新任务...")
                
                user_ids = self._get_all_user_ids()
                if not user_ids:
                    logger.info("未找到任何用户，跳过本轮长期记忆更新。")
                else:
                    logger.info(f"将为 {len(user_ids)} 位用户更新长期记忆。")
                    for user_id in user_ids:
                        # 在处理每个用户前再次检查，以便能快速响应停止信号
                        if self._stop_event.is_set():
                            logger.info("在处理用户列表时检测到停止信号，中断更新。")
                            break
                        try:
                            logger.info(f"正在为用户 {user_id} 更新长期记忆...")
                            self.update_long_term_memory(user_id)
                        except Exception as e:
                            logger.error(f"为用户 {user_id} 更新长期记忆失败: {e}")

                logger.info("本轮长期记忆更新任务完成。")

            except Exception as e:
                logger.error(f"后台长期记忆更新工作线程出现严重错误: {e}")
            
            # 等待下一次执行（每周）
            logger.info(f"下一次长期记忆更新将在 {WEEKLY_INTERVAL // (24 * 3600)} 天后执行。")
            if self._stop_event.wait(WEEKLY_INTERVAL):
                break # 如果在等待期间被停止，则跳出循环

        logger.info("后台长期记忆更新工作线程已停止。")

    def start_long_term_memory_update_thread(self):
        """启动长期记忆更新的后台线程"""
        if not hasattr(self, '_worker_thread') or not self._worker_thread.is_alive():
            self._stop_event = threading.Event()
            self._worker_thread = threading.Thread(target=self._long_term_memory_worker_loop, daemon=True)
            self._worker_thread.start()
            logger.info("成功启动后台长期记忆更新工作线程。")
            return True
        logger.info("后台长期记忆更新工作线程已在运行中。")
        return False
    
    def update_long_term_memory(self,user_id:str): #定期更新长期记忆
        """定期将短期记忆总结为长期记忆（人物小传更新）"""
        try:
            # 获取最近的事件记忆，增加数量以适应周更频率
            recent_events_res = get_recent_events(index_name=self.event_index, user_id=user_id, size=200) 
            if recent_events_res.get('result') != 'success' or not recent_events_res.get('events'):
                return {"result":"success","message":"没有新的事件记忆需要处理"}
            
            recent_events = recent_events_res['events']
            
            # 按人物分组事件
            person_events=self._group_events_by_person(recent_events)
            
            updated_persons=[]
            for person_identifier, events in person_events.items():
                # person_identifier 可能是 person_id 或者人名
                # 先尝试按 person_id 查找，如果失败再按名称查找
                person = None

                # 尝试按 person_id 查找
                person_result = get_person_by_id_mysql(user_id=user_id, person_id=person_identifier)
                if person_result.get("result") == "success" and person_result.get("person"):
                    person = person_result["person"]
                else:
                    # 按名称查找（兜底逻辑，处理旧数据或未找到档案的情况）
                    search_result = search_persons_by_name_mysql(user_id=user_id, name=person_identifier, limit=1)
                    if search_result.get("result") == "success" and search_result.get("persons"):
                        person = search_result["persons"][0]

                if person:
                    # 总结事件为人物小传
                    updated_summary=self._summarize_events_to_profile(person,events)
                    if updated_summary:
                        # 更新人物档案
                        update_result=update_person_mysql(user_id=user_id, person_id=person["person_id"], profile_summary=updated_summary)
                        if update_result["result"]=="success":
                            updated_persons.append(person.get("canonical_name", person_identifier))
            
            return {"result":"success","updated_persons":updated_persons}
        except Exception as e:
            logger.error(f"更新长期记忆失败: {str(e)}")
            return {"result":"error","reason":str(e)}

    def _group_events_by_person(self,events:List[dict]): #按人物分组事件
        person_events={}
        for event in events:
            participants=event.get("participants",[])
            for participant_id in participants:
                # 现在 participant_id 可能是 person_id 或者人名（如果没找到对应档案）
                if participant_id != "我":  # 排除用户自己
                    if participant_id not in person_events:
                        person_events[participant_id]=[]
                    person_events[participant_id].append(event)
        return person_events
    
    def _summarize_events_to_profile(self,person:dict,events:List[dict]): #将事件总结为人物小传
        """调用LLM，根据人物现有信息和新事件，生成更新后的人物小传"""
        try:
            prompt_template=get_value("humanrelation.long_memory_summarization_prompt")
            if not prompt_template:
                logger.error("长期记忆总结提示词 'humanrelation.long_memory_summarization_prompt' 未在配置中心配置。")
                return None

            current_date_str = datetime.now().strftime('%Y-%m-%d')
            prompt = prompt_template.format(current_date=current_date_str)
            
            # 递归把 datetime 转字符串以防json序列化失败
            def dt2str(obj):
                from datetime import datetime
                if isinstance(obj, dict):
                    return {k: dt2str(v) for k,v in obj.items()}
                if isinstance(obj, list):
                    return [dt2str(v) for v in obj]
                if isinstance(obj, datetime):
                    return obj.isoformat()
                return obj

            context_data={"person":dt2str(person),"events":dt2str(events)}
            
            query_input={"model":get_value("humanrelation.memory_extraction_model","gpt-4o-mini"),"messages":[{"role":"system","content":prompt},{"role":"user","content":json.dumps(context_data,ensure_ascii=False)}],"stream":False,"temperature":0.5,"max_tokens":800}
            
            response=send_to_ai(query_input)
            response_data=json.loads(response.text)
            summary=response_data["choices"][0]["message"]["content"].strip()
            
            return summary
        except Exception as e:
            logger.error(f"总结人物小传失败: {str(e)}")
            return None

    def check_outdated_information(self, user_id: str, days_threshold: int = 180): #检查过期信息
        """检查并标记可能过期的信息点"""
        try:
            all_persons_res = get_all_persons_mysql(user_id=user_id, limit=1000)
            if all_persons_res.get("result") != "success": return
            
            outdated_persons = []
            cutoff_date=datetime.now()-timedelta(days=days_threshold)
            
            for person in all_persons_res["persons"]:
                key_attributes=person.get("key_attributes",{})
                for key,value in key_attributes.items():
                    if key.endswith("_更新时间"):
                        try:
                            update_time=datetime.fromisoformat(value)
                            if update_time<cutoff_date:
                                field_name=key.replace("_更新时间","")
                                outdated_persons.append({"person_id":person["person_id"],"person_name":person["canonical_name"],"field":field_name,"value":key_attributes.get(field_name),"last_update":value})
                        except:
                            pass
            
            return {"result": "success", "outdated_info": outdated_persons}
        except Exception as e:
            logger.error(f"检查过期信息失败: {e}")
            return {"result": "error", "reason": str(e)}
    
    def _handle_alias_relation(self,data:dict, user_id:str):
        """处理别名关系：A是B，将B作为A的别名"""
        try:
            original_name=data.get("original_name","")
            alias_name=data.get("alias_name","")
            
            if not original_name or not alias_name: 
                return {"action_code":"ERROR","payload":{"reason":"别名关系信息不完整"}}
            
            # 查找原始人物
            search_result=search_persons_by_name_mysql(user_id=user_id, name=original_name, limit=1)
            if not search_result.get("persons"):
                return {"action_code":"ERROR","payload":{"reason":f"找不到人物: {original_name}"}}
            
            person=search_result["persons"][0]
            try:
                current_aliases=json.loads(person.get("aliases","[]")) if person.get("aliases") else []
                if not isinstance(current_aliases, list): current_aliases = []
            except (json.JSONDecodeError, TypeError):
                current_aliases = []

            # 检查别名是否已存在于其他档案中
            alias_search=search_persons_by_name_mysql(user_id=user_id, name=alias_name, limit=5)
            if alias_search.get("persons"):
                # 如果别名已作为其他人的正式姓名存在，需要合并
                for existing_person in alias_search["persons"]:
                    if existing_person["canonical_name"]==alias_name:
                        return self._merge_two_persons(user_id, person["person_id"], existing_person["person_id"])
            
            # 添加别名
            if alias_name not in current_aliases:
                current_aliases.append(alias_name)
                update_result=update_person_mysql(user_id=user_id, person_id=person["person_id"], aliases=json.dumps(current_aliases,ensure_ascii=False))
                if update_result["result"]=="success":
                    logger.info(f"为{original_name}添加别名{alias_name}")
                    return {"action_code":"PROCESS_COMPLETE","payload":{"message":f"已将'{alias_name}'添加为'{original_name}'的别名"}}
            
            return {"action_code":"PROCESS_COMPLETE","payload":{"message":"别名已存在"}}
        except Exception as e:
            logger.error(f"处理别名关系失败: {e}")
            return {"action_code":"ERROR","payload":{"reason":str(e)}}

    def _handle_rename_relation(self,data:dict, user_id:str):
        """处理正名关系：A的大名叫B，将A的正式姓名改为B"""
        try:
            current_name=data.get("current_name","")
            formal_name=data.get("formal_name","")
            
            if not current_name or not formal_name:
                return {"action_code":"ERROR","payload":{"reason":"正名关系信息不完整"}}
            
            # 查找当前人物
            search_result=search_persons_by_name_mysql(user_id=user_id, name=current_name, limit=1)
            if not search_result.get("persons"):
                return {"action_code":"ERROR","payload":{"reason":f"找不到人物: {current_name}"}}
            
            person=search_result["persons"][0]
            
            # 检查正式姓名是否已被其他人使用
            formal_search=search_persons_by_name_mysql(user_id=user_id, name=formal_name, limit=5)
            if formal_search.get("persons"):
                for existing_person in formal_search["persons"]:
                    if existing_person["canonical_name"]==formal_name and existing_person["person_id"]!=person["person_id"]:
                        # 需要合并两个档案
                        return self._merge_two_persons(user_id, existing_person["person_id"], person["person_id"])
            
            # 更新正式姓名，将原名加入别名
            try:
                current_aliases=json.loads(person.get("aliases","[]")) if person.get("aliases") else []
                if not isinstance(current_aliases, list): current_aliases = []
            except (json.JSONDecodeError, TypeError):
                current_aliases = []

            if person["canonical_name"] not in current_aliases:
                current_aliases.append(person["canonical_name"])
            
            update_result=update_person_mysql(user_id=user_id, person_id=person["person_id"], canonical_name=formal_name, aliases=json.dumps(current_aliases,ensure_ascii=False))
            if update_result["result"]=="success":
                logger.info(f"将{current_name}的正式姓名更新为{formal_name}")
                return {"action_code":"PROCESS_COMPLETE","payload":{"message":f"已将'{current_name}'的正式姓名更新为'{formal_name}'"}}
            
            return {"action_code":"ERROR","payload":{"reason":"更新失败"}}
        except Exception as e:
            logger.error(f"处理正名关系失败: {e}")
            return {"action_code":"ERROR","payload":{"reason":str(e)}}

    def _merge_two_persons(self, user_id:str, primary_person_id:str, secondary_person_id:str):
        """内部合并两个人物档案的辅助方法"""
        try:
            result=self.execute_merge_persons(user_id, primary_person_id, secondary_person_id)
            if result["result"]=="success":
                return {"action_code":"PROCESS_COMPLETE","payload":{"message":"档案已自动合并"}}
            else:
                return {"action_code":"ERROR","payload":{"reason":result.get("reason","合并失败")}}
        except Exception as e:
            logger.error(f"合并档案失败: {e}")
            return {"action_code":"ERROR","payload":{"reason":str(e)}}

    def _replace_relative_time(self, text: str) -> str:
        """将文本中的相对时间（如"昨天"）替换为绝对日期（YYYY-MM-DD）"""
        if not isinstance(text, str): return text
        
        today = datetime.now().date()
        time_map = {
            "前天": today - timedelta(days=2),
            "昨天": today - timedelta(days=1),
            "今天": today,
            "明天": today + timedelta(days=1),
            "后天": today + timedelta(days=2),
        }
        
        original_text = text
        for word, date_obj in time_map.items():
            if word in text:
                text = text.replace(word, f"{word}({date_obj.strftime('%Y-%m-%d')})")
        
        if text != original_text:
            logger.info(f"时间表达式转换: '{original_text}' -> '{text}'")

        return text 