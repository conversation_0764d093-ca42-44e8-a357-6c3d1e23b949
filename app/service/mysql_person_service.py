########################################################
# MySQL人员服务 - 替代ES人员服务
########################################################

from utils.logger import logger
from my_mysql.entity.person_table import insert_person,get_all_persons,get_person_by_id,update_person,search_persons_by_name,delete_person
from uuid import uuid4
from typing import List,Optional
import json

def add_person(user_id:str, canonical_name:str="",aliases:str="",relationships:list=[],profile_summary:str="",key_attributes:dict={},avatar:str="",is_user:bool=False,person_id:str=""): #添加人员到MySQL
    if not person_id:person_id=str(uuid4())
    try:
        success=insert_person(user_id=user_id,person_id=person_id,is_user=is_user,canonical_name=canonical_name,aliases=aliases,relationships=relationships,profile_summary=profile_summary,key_attributes=key_attributes,avatar=avatar)
        if success:
            logger.info(f"MySQL添加人员成功: {person_id} for user: {user_id}")
            return {"result":"success","person_id":person_id}
        else:
            return {"result":"error","reason":"插入失败"}
    except Exception as e:
        logger.error(f"MySQL添加人员失败: {str(e)}")
        return {"result":"error","reason":str(e)}

def get_all_persons_mysql(user_id:str, limit:int=100,offset:int=0): #获取所有人员列表
    try:
        persons=get_all_persons(user_id=user_id, limit=limit,offset=offset)
        processed_persons=[]
        default_attributes={"关系":"","年龄":"","职业":""}

        for person in persons:
            # 确保 key_attributes 不是 None
            raw_attributes=person.get('key_attributes')
            if raw_attributes:
                # 确保是字典而非JSON字符串
                key_attributes = json.loads(raw_attributes) if isinstance(raw_attributes, str) else raw_attributes
            else:
                key_attributes = {}
            
            # 补全必需字段
            person['key_attributes']={**default_attributes,**key_attributes}

            # 确保 relationships 是列表或null
            raw_relationships=person.get('relationships')
            if raw_relationships:
                 person['relationships'] = json.loads(raw_relationships) if isinstance(raw_relationships, str) else raw_relationships
            else:
                person['relationships'] = None # 遵从接口定义，返回null

            processed_persons.append(person)
        
        logger.info(f"MySQL获取人员列表成功，共{len(processed_persons)}人 for user: {user_id}")
        return {"result":"success","persons":processed_persons}
    except Exception as e:
        logger.error(f"MySQL获取人员列表失败: {str(e)}")
        return {"result":"error","reason":str(e),"persons":[]}

def get_person_by_id_mysql(user_id:str, person_id:str): #根据ID获取人员详情
    try:
        person=get_person_by_id(user_id=user_id, person_id=person_id)
        if person:
            logger.info(f"MySQL获取人员详情成功: {person_id} for user: {user_id}")
            return {"result":"success","person":person}
        else:
            return {"result":"error","reason":"人员不存在","person":None}
    except Exception as e:
        logger.error(f"MySQL获取人员详情失败: {str(e)}")
        return {"result":"error","reason":str(e),"person":None}

def update_person_mysql(user_id:str, person_id:str,**kwargs): #更新人员信息
    try:
        success=update_person(user_id=user_id, person_id=person_id,**kwargs)
        if success:
            logger.info(f"MySQL更新人员成功: {person_id} for user: {user_id}")
            return {"result":"success"}
        else:
            return {"result":"error","reason":"更新失败或人员不存在"}
    except Exception as e:
        logger.error(f"MySQL更新人员失败: {str(e)}")
        return {"result":"error","reason":str(e)}

def search_persons_by_name_mysql(user_id:str, name:str,limit:int=10): #按姓名搜索人员
    try:
        persons=search_persons_by_name(user_id=user_id, name=name,limit=limit)
        
        # 对返回结果进行处理，确保JSON字段被正确解析
        processed_persons = []
        for person in persons:
            # 确保 key_attributes 是字典
            raw_attributes = person.get('key_attributes')
            if raw_attributes and isinstance(raw_attributes, str):
                person['key_attributes'] = json.loads(raw_attributes)
            elif not raw_attributes:
                person['key_attributes'] = {}

            # 确保 relationships 是列表或 None
            raw_relationships = person.get('relationships')
            if raw_relationships and isinstance(raw_relationships, str):
                person['relationships'] = json.loads(raw_relationships)
            elif not raw_relationships:
                person['relationships'] = None
            
            processed_persons.append(person)

        # 将结果简要序列化（最多显示前2条，防止日志过长）
        try:
            import json as _json
            preview=_json.dumps(processed_persons[:2],ensure_ascii=False, default=str)
        except Exception:
            preview=str(processed_persons[:2])
        logger.info(f"MySQL搜索人员成功，关键词:{name}，结果数:{len(processed_persons)} for user: {user_id}，预览:{preview}")
        return {"result":"success","persons":processed_persons}
    except Exception as e:
        logger.error(f"MySQL搜索人员失败: {str(e)}")
        return {"result":"error","reason":str(e),"persons":[]}

def delete_person_mysql(user_id:str, person_id:str): #删除人员
    try:
        success=delete_person(user_id=user_id, person_id=person_id)
        if success:
            logger.info(f"MySQL删除人员成功: {person_id} for user: {user_id}")
            return {"result":"success"}
        else:
            return {"result":"error","reason":"删除失败或人员不存在"}
    except Exception as e:
        logger.error(f"MySQL删除人员失败: {str(e)}")
        return {"result":"error","reason":str(e)}

def search_persons_by_alias(user_id:str, alias:str,limit:int=10): #按别名搜索人员
    try:
        from my_mysql.entity.person_table import person_memory
        from my_mysql import sql_client
        from sqlalchemy import select
        stmt=select(person_memory).where(person_memory.c.user_id == user_id, person_memory.c.aliases.like(f'%{alias}%')).limit(limit)
        results=sql_client.select_many(stmt)
        persons=[]
        for row in results:
            person=dict(row._mapping)
            if person['relationships']:person['relationships']=json.loads(person['relationships'])
            if person['key_attributes']:person['key_attributes']=json.loads(person['key_attributes'])
            persons.append(person)
        logger.info(f"MySQL按别名搜索人员成功，关键词:{alias}，结果数:{len(persons)} for user: {user_id}")
        return {"result":"success","persons":persons}
    except Exception as e:
        logger.error(f"MySQL按别名搜索人员失败: {str(e)}")
        return {"result":"error","reason":str(e),"persons":[]} 