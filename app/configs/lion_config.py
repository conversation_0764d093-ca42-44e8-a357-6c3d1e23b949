from .config import CURRENT_ENV, APP_KEY
import threading

import json

# -*- coding: utf-8 -*-
import hashlib
import hmac
from base64 import encodebytes
from datetime import datetime
from urllib import parse
from utils.logger import logger

import requests



class LionClient(object):
    def __init__(self, client_id: str,
                 client_secret: str,
                 env: str,
                 app_name: str):
        '''
        Args:
            client_id:      从lion那边申请到的 ba id （虚拟账号）
            client_secret:  从lion那边申请到的 ba token （lion为虚拟账号生成的密码 ）
            app_name:       项目名，e.g. com.sankuai.dialogstudio.xiaomei.toolexecute
            env:            环境 [dev,test,ppe,staging,prod]

        '''
        self.client_id = client_id
        self.client_secret = client_secret
        self.app_name = app_name
        self.env = env
        self.domain = self.get_domain()  # 根据指定环境获取 url

    def post_auth(self, client_id: str, client_secret: str, url: str):
        '''
        鉴权
        :return:
        '''
        gmt_time = datetime.utcnow().strftime('%a, %d %b %Y %H:%M:%S GMT')
        string2sign = "GET %s\n%s" % (parse.urlparse(url).path, gmt_time)
        signature = encodebytes(hmac.new(str.encode(client_secret), str.encode(string2sign), hashlib.sha1).digest())
        return dict(Date=gmt_time, Authorization="MWS " + client_id + ":" + bytes.decode(signature).replace("\n", ""))

    def get_domain(self):
        '''
        根据不同的环境获取 url
            线上：http://lion.vip.sankuai.com，包含环境 env = prod、staging；
            线下：http://lion-api.inf.test.sankuai.com，包含环境 env = dev、test；
        '''
        if self.env in ['prod', 'staging']:
            return 'http://lion.vip.sankuai.com'
        else:
            return 'http://lion-api.inf.test.sankuai.com'

    def get_value_from_lion(self, lion_key: str) -> str:
        '''
        根据 key 从 lion 中获取配置的 value
        Args:
            lion_key: 创建时的 key，e.g.galileo_prompt.summary"
        '''

        url = self.domain + '/v3/configs/envs/{}/appkeys/{}/keys/{}'.format(self.env, self.app_name, lion_key)
        try:
            auth = self.post_auth(self.client_id, self.client_secret, url)
            response_json = requests.get(url, headers=auth, timeout=5).json()   
            if response_json is None:
                return ""
            result = response_json.get('result', {})
            return result.get('value', "")
        except Exception as e:
            logger.error(f"get_value_from_lion failed., lion_key: {lion_key}")
            logger.exception(e)
            return ""

    def get_value(self, lion_key: str, default=None) -> str:
        '''
        获取单个lion配置value
        （可以做个 cache，但是 yangwenqing 建议，现在这种「直接调用而不是批量跑全部」情况下可以实时从 lion 里拿）
        Args:
            lion_key: 创建时的 key，e.g.galileo_prompt.summary"
        :return:
        '''
        try:
            res = self.get_value_from_lion(lion_key)
            if res:
                return res
            else:
                return default
        except Exception as e:
            logger.error(f"get_value failed., lion_key: {lion_key}")
            logger.exception(e)
            return default



LION_CLIENT_ID = "xiaomei_toolexecute_admin"
LION_SECRET = "XFDCTVU8SC"

lion_client = LionClient(client_id=LION_CLIENT_ID,
                         client_secret=LION_SECRET,
                         env=CURRENT_ENV,   
                         app_name=APP_KEY)


def get_value(key, default=None):
    # from utils.logger import logger
    try:
        return lion_client.get_value(key, default)
    except Exception as e:
        logger.error(f"Failed to get value from lion: {e}, return default instead: {default}")
        return default
