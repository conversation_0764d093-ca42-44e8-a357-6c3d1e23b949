import signal
import sys
import uvicorn
from app import app

print("---------------- wsgi start ----------\n\n")

if __name__ == "__main__":
    try:
        # uvicorn.run 会自动处理信号，不需要手动注册信号处理器
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8080,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("收到中断信号，服务器正在关闭...")
    except Exception as e:
        print(f"服务器运行出错: {str(e)}")
        sys.exit(1)