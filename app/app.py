import threading
import time
from datetime import datetime
from typing import Optional

import fastapi
import uvicorn
from agents.hr import get_agent_response_stream, graph
from configs.config import APP_KEY
from configs.lion_config import get_value
from fastapi import Body
from fastapi.responses import StreamingResponse
from langchain.schema import HumanMessage
from my_mysql.entity.reminders import insert_reminder, query_reminders_by_status_and_time
from pycat import Cat
from pydantic import BaseModel
from service.enhanced_memory_service import EnhancedMemoryService  # 增强记忆服务
from service.ESmemory.es_memory_callback import search_memory
from service.mysql_person_service import add_person  # 改为MySQL人员服务
from service.mysql_person_service import (
    delete_person_mysql,
    get_all_persons_mysql,
    get_person_by_id_mysql,
    search_persons_by_name_mysql,
    update_person_mysql,
)
from utils.logger import logger

Cat.init_cat(APP_KEY)
Cat.init_cat(APP_KEY)

# 全局变量用于追踪后台线程
reminder_thread = None
thread_start_time = None

PROMPT_TEMPLATE = get_value(
    "reminder.prompt.template",
    "请根据以下信息生成一条温馨的提醒：{reminder_text_template}",
)


def reminder_background_task():
    """
    后台任务：每10分钟查询一次需要处理的提醒
    """
    # logger.info("后台提醒任务线程开始运行")
    while True:
        try:
            # 查询状态为'active'且触发时间已到的提醒
            current_time = datetime.now()  # TODO: 这里的时间需要弄好，决定好时间的格式，比如YYYY-MM-DD
            active_reminders = query_reminders_by_status_and_time("active", current_time)

            if active_reminders:
                # logger.info(f"发现 {len(active_reminders)} 条需要处理的提醒")
                columns = [
                    "reminder_id",
                    "user_id",
                    "subject_person_id",
                    "reminder_text_template",
                    "base_event_date",
                    "advance_notice_config",
                    "recurrence_rule",
                    "next_trigger_time",
                    "status",
                    "created_at",
                    "updated_at",
                ]
                for reminder in active_reminders:
                    reminder_dict = dict(zip(columns, reminder))
                    # logger.info(f"提醒ID: {reminder_dict['reminder_id']}, "
                    #           f"用户ID: {reminder_dict['user_id']}, "
                    #           f"触发时间: {reminder_dict['next_trigger_time']}")

                    # 1. 生成提醒内容
                    reminder_content = generate_reminder_content(reminder_dict)

                    # 2. 推送提醒
                    send_reminder_to_user(reminder_dict["user_id"], reminder_content)

                    # 3. 更新提醒状态为completed，避免重复推送
                    from my_mysql.entity.reminders import update_reminder_status

                    update_reminder_status(reminder_dict["reminder_id"], "completed")
            else:
                # logger.info("当前没有需要处理的提醒")
                pass
        except Exception as e:
            logger.error(f"后台提醒任务执行失败: {e}")

        # 休眠10分钟 (600秒)
        time.sleep(600)


def start_reminder_thread():
    """
    启动提醒后台线程
    """
    global reminder_thread, thread_start_time

    if reminder_thread and reminder_thread.is_alive():
        logger.warning("提醒线程已经在运行中")
        return False

    reminder_thread = threading.Thread(target=reminder_background_task, daemon=True)
    reminder_thread.start()
    thread_start_time = datetime.now()
    logger.info("后台提醒检查线程已启动")
    return True


def is_reminder_thread_alive():
    """
    检查提醒线程是否还活着
    """
    global reminder_thread
    return reminder_thread is not None and reminder_thread.is_alive()


app = fastapi.FastAPI()

index_name = get_value("humanrelation.memory_index_name", "default_memory_index")

# 初始化增强记忆服务
enhanced_memory = EnhancedMemoryService()


class SearchMemoryRequest(BaseModel):
    user_input: str  # 用户输入内容，根据语义相关性查询
    size: int  # 返回结果数量
    k: int  # 候选数量
    user_id: str  # 查询者的用户 mis id
    memory_type: str  # 记忆类型，分long or short


class ChatRequest(BaseModel):
    content: str  # 用户问题
    conversation_id: str  # 对话 ID，用于上下文关联
    user_id: str  # 用户ID


class AddMemoryRequest(BaseModel):
    conversation_text: str  # 对话文本
    user_id: str  # 用户ID


@app.get("/")
def read_root():
    return "Hello World"


class ReadMemoryRequest(BaseModel):
    query_text: str  # 查询文本
    user_id: str  # 用户ID
    max_results: int = 10  # 最大结果数


class PersonRequest(BaseModel):
    user_id: str  # 用户ID
    canonical_name: str  # 正式姓名
    aliases: str = ""  # 别名
    relationships: list = []  # 人际关系
    profile_summary: str = ""  # 个人简介
    key_attributes: dict = {}  # 关键属性
    avatar: str = ""  # 头像URL
    is_user: bool = False  # 是否为用户


class MergePersonsRequest(BaseModel):
    user_id: str  # 用户ID
    primary_person_id: str
    secondary_person_id: str


@app.get("/humanrelation/reminder_thread_status")
def check_reminder_thread_status():
    """
    监察接口：检查后台提醒线程状态
    """
    is_alive = is_reminder_thread_alive()
    status_message = "线程正常运行" if is_alive else "线程已停止"
    start_time_str = thread_start_time.strftime("%Y-%m-%d %H:%M:%S") if thread_start_time else None

    result = {
        "is_alive": is_alive,
        "start_time": start_time_str,
        "status_message": status_message,
    }
    return result


@app.post("/humanrelation/restart_reminder_thread")
def restart_reminder_thread():
    """
    重启后台提醒线程
    """
    try:
        success = start_reminder_thread()
        if success:
            return {"success": True, "message": "后台提醒线程重启成功"}
        else:
            return {"success": False, "message": "线程已在运行中，无需重启"}
    except Exception as e:
        logger.error(f"重启提醒线程失败: {e}")
        return {"success": False, "message": f"重启失败: {str(e)}"}


@app.post("/humanrelation/search_memory")
def search_memory_endpoint(request: SearchMemoryRequest):
    result = search_memory(
        index_name=index_name,
        user_input=request.user_input,
        size=request.size,
        k=request.k,
        user_id=request.user_id,
        memory_type=request.memory_type,
    )
    return result


class SearchMemoryByPersonIdRequest(BaseModel):
    user_id: str
    person_id: str


@app.post("/humanrelation/search_memory_by_person_id", summary="根据人员ID搜索记忆")
def search_memory_by_person_id_endpoint(request: SearchMemoryByPersonIdRequest):
    """根据人员ID搜索记忆"""
    from service.ESmemory.es_event_service import search_events_by_participant

    event_index = get_value("humanrelation.event_index_name", "memory_event_store")
    return search_events_by_participant(event_index, request.user_id, request.person_id)


# 根据userID获取对用的所有短期记忆
@app.get(
    "/humanrelation/get_all_short_memory_by_user_id",
    summary="根据userID获取对用的所有短期记忆",
)
def get_all_short_memory_by_user_id_endpoint(user_id: str, size: int = 100):
    """根据userID获取对用的所有短期记忆"""
    from service.ESmemory.es_event_service import get_recent_events

    event_index = get_value("humanrelation.event_index_name", "memory_event_store")
    return get_recent_events(event_index, user_id, size)


# 根据userID获取所有的person_id
@app.get(
    "/humanrelation/get_all_person_id_by_user_id",
    summary="根据userID获取所有的person_id",
)
def get_all_person_id_by_user_id_endpoint(user_id: str):
    """根据userID获取所有的person_id"""
    try:
        persons_result = get_all_persons_mysql(user_id, limit=1000, offset=0)
        if persons_result.get("result") == "success":
            person_ids = [person["person_id"] for person in persons_result.get("persons", [])]
            return {
                "result": "success",
                "person_ids": person_ids,
                "count": len(person_ids),
            }
        else:
            return {
                "result": "error",
                "reason": persons_result.get("reason", "未知错误"),
                "person_ids": [],
            }
    except Exception as e:
        logger.error(f"获取所有person_id失败: {str(e)}")
        return {"result": "error", "reason": str(e), "person_ids": []}


@app.post("/humanrelation/chat", summary="流式聊天接口")
def chat_endpoint(request: ChatRequest):
    """流式聊天接口，使用 conversation_id 维护上下文"""
    response_stream = get_agent_response_stream(request.content, request.conversation_id, request.user_id)
    return StreamingResponse(response_stream, media_type="text/event-stream")


@app.post("/humanrelation/chat_json")
def chat_json_endpoint(request: ChatRequest):
    """非流式聊天接口，返回JSON格式"""
    try:
        # 使用同步方式调用记忆服务

        inputs = {
            "messages": [HumanMessage(content=request.content)],
            "user_id": request.user_id,
        }
        config = {
            "configurable": {
                "thread_id": request.conversation_id,
                "user_id": request.user_id,
            }
        }

        # 同步调用图
        result = graph.invoke(inputs, config=config)

        # 提取AI回复
        if result and "messages" in result and result["messages"]:
            ai_message = result["messages"][-1]
            content = ai_message.content if hasattr(ai_message, "content") else str(ai_message)
            return {"response": content, "status": "success"}
        else:
            return {"response": "抱歉，我暂时无法回答您的问题。", "status": "error"}
    except Exception as e:
        return {"response": f"处理请求时发生错误: {str(e)}", "status": "error"}


@app.post("/humanrelation/add_memory", summary="添加记忆")
def add_memory(request: AddMemoryRequest):
    """从对话文本中提取并存储人员和事件记忆"""
    return enhanced_memory.extract_and_process_memory(request.conversation_text, request.user_id)


@app.post("/humanrelation/read_memory", summary="读取记忆")
def read_memory(request: ReadMemoryRequest):
    """根据查询文本检索相关的人员和事件记忆"""
    return enhanced_memory.retrieve_memory_for_conversation(request.query_text, request.user_id, request.max_results)


@app.get("/humanrelation/persons", summary="查看所有人列表")
def get_persons(user_id: str, limit: int = 100, offset: int = 0):
    """获取所有人员列表"""
    return get_all_persons_mysql(user_id, limit, offset)


@app.get("/humanrelation/person/{person_id}", summary="查看某个人")
def get_person(user_id: str, person_id: str):
    """根据ID获取人员详细信息"""
    return get_person_by_id_mysql(user_id, person_id)


@app.delete("/humanrelation/person/{person_id}", summary="删除某个人")
def delete_person(user_id: str, person_id: str):
    """删除指定人员"""
    return delete_person_mysql(user_id, person_id)


@app.post("/humanrelation/add_person", summary="增加某个人")
def create_person(request: PersonRequest):
    """添加新人员"""
    return add_person(
        user_id=request.user_id,
        canonical_name=request.canonical_name,
        aliases=request.aliases,
        relationships=request.relationships,
        profile_summary=request.profile_summary,
        key_attributes=request.key_attributes,
        avatar=request.avatar,
        is_user=request.is_user,
    )


@app.put("/humanrelation/change_person/{person_id}", summary="修改某个人的档案")
def update_person(person_id: str, request: PersonRequest):
    """更新人员档案信息"""
    return update_person_mysql(
        user_id=request.user_id,
        person_id=person_id,
        canonical_name=request.canonical_name,
        aliases=request.aliases,
        relationships=request.relationships,
        profile_summary=request.profile_summary,
        key_attributes=request.key_attributes,
        avatar=request.avatar,
        is_user=request.is_user,
    )


@app.get("/humanrelation/search_person", summary="搜索某个人")
def search_person(user_id: str, name: str, limit: int = 10):
    """按姓名搜索人员"""
    return search_persons_by_name_mysql(user_id, name, limit)


@app.post("/humanrelation/update_long_memory", summary="更新长期记忆")
def update_long_memory(user_id: str):
    """手动触发长期记忆更新，将短期记忆总结为人物小传"""
    return enhanced_memory.update_long_term_memory(user_id)


@app.get("/humanrelation/check_outdated", summary="检查过期信息")
def check_outdated_info(user_id: str, days: int = 180):
    """检查需要更新的过期状态信息"""
    return enhanced_memory.check_outdated_information(user_id, days)


@app.post("/humanrelation/merge_persons", summary="合并两个人员档案")
def merge_persons(request: MergePersonsRequest):
    """确认并执行两个人员档案的合并"""
    return enhanced_memory.execute_merge_persons(
        request.user_id, request.primary_person_id, request.secondary_person_id
    )


@app.get("/monitor/alive", summary="健康检查接口")
def health_check():
    """健康检查接口，用于部署时的存活性检查"""
    return {"status": "ok", "message": "Service is alive"}


@app.get("/humanrelation/history", summary="获取聊天记录")
def get_history(conversation_id: str, user_id: str):
    """根据对话ID获取完整的聊天记录"""
    try:
        # graph.checkpointer is now an instance of MySQLCheckpointSaver
        if not graph.checkpointer:
            return {"status": "error", "message": "Checkpointer not initialized"}
        history = graph.checkpointer.get_history(conversation_id, user_id)  # type: ignore
        return {
            "conversation_id": conversation_id,
            "history": history,
            "status": "success",
        }
    except Exception as e:
        logger.error(f"获取聊天记录失败: conversation_id={conversation_id}, error={e}")
        return {"status": "error", "message": str(e)}


class CreateConversationRequest(BaseModel):
    user_id: str


@app.post("/humanrelation/create_conversation", summary="生成新的会话ID")
def create_conversation_endpoint(request: CreateConversationRequest):
    """
    为新对话生成一个唯一的、带时间戳的 conversation_id.
    """
    try:
        user_id = request.user_id
        # 对user_id进行简化处理，确保生成的ID格式清晰
        safe_user_id = "".join(e for e in user_id if e.isalnum())
        timestamp = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
        conversation_id = f"{safe_user_id}_{timestamp}"
        return {"success": True, "conversation_id": conversation_id}
    except Exception as e:
        logger.error(f"生成conversation_id失败: {e}")
        return {"success": False, "message": str(e)}


@app.get("/humanrelation/conversations", summary="获取用户的所有会话ID")
def get_user_conversations_endpoint(user_id: str):
    """
    根据用户ID获取其所有历史对话的 conversation_id 列表.
    """
    try:
        if not graph.checkpointer:
            return {"success": False, "message": "Checkpointer not initialized"}
        conversation_ids = graph.checkpointer.get_conversation_ids(user_id)  # type: ignore
        return {
            "success": True,
            "user_id": user_id,
            "conversation_ids": conversation_ids,
        }
    except Exception as e:
        logger.error(f"获取 {user_id} 的会话列表失败: {e}")
        return {"success": False, "message": str(e)}


@app.on_event("startup")
async def startup_event():
    """
    应用启动事件：启动后台提醒检查线程
    """
    try:
        started = start_reminder_thread()
        if started:
            logger.info("应用启动时，后台提醒检查线程已成功启动。")
        else:
            logger.info("应用启动时，后台提醒检查线程已在运行，无需重复启动。")
    except Exception as e:
        logger.error(f"应用启动时启动提醒线程失败: {e}")

    # 启动长期记忆更新工作线程
    try:
        worker_started = enhanced_memory.start_long_term_memory_update_thread()
        if worker_started:
            logger.info("应用启动时，后台长期记忆更新工作线程已成功启动。")
        else:
            logger.info("应用启动时，后台长期记忆更新工作线程已在运行，无需重复启动。")
    except Exception as e:
        logger.error(f"应用启动时启动长期记忆更新工作线程失败: {e}")


class AddReminderRequest(BaseModel):
    user_id: str
    base_event_date: datetime
    next_trigger_time: datetime
    subject_person_id: Optional[str] = None
    reminder_text_template: Optional[str] = None
    advance_notice_config: Optional[dict] = None
    recurrence_rule: Optional[str] = None
    status: str = "active"


@app.post("/humanrelation/add_reminder", summary="添加提醒")
def add_reminder(request: AddReminderRequest = Body(...)):
    """
    添加一条提醒到MySQL reminders表
    """
    reminder_id = insert_reminder(
        user_id=request.user_id,
        base_event_date=request.base_event_date,
        next_trigger_time=request.next_trigger_time,
        subject_person_id=request.subject_person_id,
        reminder_text_template=request.reminder_text_template,
        advance_notice_config=request.advance_notice_config,
        recurrence_rule=request.recurrence_rule,
        status=request.status,
    )
    if reminder_id:
        return {"success": True, "reminder_id": reminder_id}
    else:
        return {"success": False, "message": "插入失败"}


def generate_reminder_content(reminder_dict):
    prompt = PROMPT_TEMPLATE.format(reminder_text_template=reminder_dict["reminder_text_template"])
    # 这里用你现有的ai_client/graph接口调用大模型
    from agents.hr import graph
    from langchain.schema import HumanMessage

    inputs = {
        "messages": [HumanMessage(content=prompt)],
        "user_id": reminder_dict["user_id"],
    }
    config = {
        "configurable": {
            "thread_id": str(reminder_dict["reminder_id"]),
            "user_id": reminder_dict["user_id"],
        }
    }
    result = graph.invoke(inputs, config=config)
    if result and "messages" in result and result["messages"]:
        ai_message = result["messages"][-1]
        return ai_message.content if hasattr(ai_message, "content") else str(ai_message)
    return "提醒内容生成失败"


def send_reminder_to_user(user_id, content):
    # 这里可以是发微信、短信、App通知等
    logger.info(f"推送给用户{user_id}的提醒内容：{content}")


class UpdateEventRequest(BaseModel):
    user_id: str
    event_id: str
    description_text: Optional[str] = None
    participants: Optional[list] = None
    location: Optional[str] = None
    topics: Optional[list] = None
    sentiment: Optional[str] = None


@app.put("/humanrelation/update_event", summary="更新事件内容")
def update_event_endpoint(request: UpdateEventRequest):
    """更新事件内容，支持修正AI编辑错误和关联错误"""
    try:
        from service.ESmemory.es_event_service import update_event_content

        event_index = get_value("humanrelation.event_index_name", "memory_event_store")

        # 构建更新参数
        update_params = {}
        if request.description_text is not None:
            update_params["description_text"] = request.description_text
        if request.participants is not None:
            update_params["participants"] = request.participants
        if request.location is not None:
            update_params["location"] = request.location
        if request.topics is not None:
            update_params["topics"] = request.topics
        if request.sentiment is not None:
            update_params["sentiment"] = request.sentiment

        if not update_params:
            return {"success": False, "message": "请提供至少一个要更新的字段"}

        result = update_event_content(
            index_name=event_index, user_id=request.user_id, event_id=request.event_id, **update_params
        )

        if result.get("result") == "success":
            return {
                "success": True,
                "message": "事件更新成功",
                "event_id": result.get("event_id"),
                "updated_fields": result.get("updated_fields"),
            }
        else:
            return {"success": False, "message": result.get("reason", "更新失败")}

    except Exception as e:
        logger.error(f"更新事件失败: {str(e)}")
        return {"success": False, "message": f"更新失败: {str(e)}"}


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8080)
