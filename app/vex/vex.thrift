namespace java com.meituan.horus.vexv4

struct VexFeature {
  1: required list<double> vector; // 特征向量
  2: optional i32 num; // 可选，对于目标检测的情况，需要传入多目标特征以及目标数量
}

struct VexSearchRequest {
  1: required string imagesetName; // 图像检索库名称
  2: optional string tableName;    // 检索库所构建的索引名称，如果不传入，会使用默认索引名称，也就是创建检索库时第一个索引
  3: required string url; // 图像链接
  4: required i32 topk; // 检索结果数量topk
  5: optional string content; // 可选，图像内容Base64
  6: optional map<string, VexFeature> featureMap; // 特征向量Map
  7: optional list<string> entityFilters; // 实体检索时过滤实体结果
  8: optional list<string> groupFilters;  // 实体检索时过滤组结果
}

struct VexSearchItemInfo {
  1: i64 globalId; // 结果图像对应唯一id
  2: string url; // 结果图像链接
  3: string label; // 结果图像标签
  4: double similarity; // 检索图像与结果图像的特征向量相似度
  5: optional string entityName; // 实体名称
  6: optional string groupName; // 组名称
}

struct VexSearchResponse {
  1: i32 code;
  2: string message;
  3: list<VexSearchItemInfo> items; // 检索到的图像结果，如果实际结果数量不足topk个，就会少于k而不是补齐
  4: list<double> vector; // 检索图像的特征向量
  5: string others; // 其他信息，比如目标检索模型的bbox信息等
}

struct VexAddRequest {
  1: required string imagesetName; // 图像检索库名称
  2: required string url;     // 必传，图像url，即使是临时生成或者没有，也需要传入一个字符串，限制长度256
  3: required string label;   // 必传，图像标签，限制长度256
  4: optional string content; // 可选，图像内容的base64编码，如果不传，同时也没有传入特征向量，Vex就会从url下载图片然后请求特征服务
  // 特征向量map，key为特征名称，目前支持的特征名称为 dish dishv2 black blackv2 shop product_all product_egg
  // flower custom_256 custom_512，value是实际向量
  5: optional map<string, VexFeature> featureMap; // 图像特征向量，如果传入，那么就不需要传图像base64，Vex也不会下载图片和请求特征服务
  6: optional string entityName; // 可选，图像实体检索，实体名称,限制长度64
  7: optional string groupName; // 可选，图像实体检索，实体所属组名称，限制长度64
  8: optional string tableName  // 可选，表名称，如果不传入，会使用默认表名称，也就是创建检索库时第一个表，限制长度64
}

struct VexAddResponse {
  1: i32 code;
  2: string message;
  3: i64 globalId;  // 每一张添加到的向量，都会生成一个唯一int64 id，这个id用于查询以及删除图像信息，如果业务有相关需求，需要保存这个id备用
}

struct VexImage {
  1: i64 globalId;
  2: string url;
  3: string label;
  4: optional map<string, VexFeature> featureMap; // 特征向量是一个map，map的key是特征名称，value是实际向量，一张图可以提取多个模型的特征
  5: optional string entityName;
  6: optional string groupName;
}

struct VexImageResponse {
  1: i32 code;
  2: string message;
  3: optional map<i64, VexImage> imageMap; // 查询图像信息时返回的结果，key是图像的global id，value是图像信息
}

struct VexEntityResponse {
  1: i32 code;
  2: string message;
  3: optional list<VexImage> imageList;
}

struct VexDeleteEntityRequest {
  1: string imagesetName,
  2: string entityName,
  3: optional string groupName // 如果传入group，就删除指定group中的entity数据
  4: optional string tableName // 表名称，如果不传入，会使用默认表名称，也就是创建检索库时第一个表
}

struct VexDeleteRequest {
  1: string imagesetName
  2: optional string tableName // 表名称，如果不传入，会使用默认表名称，也就是创建检索库时第一个表
  3: optional i64    globalId  // 当 globalId 和 url 都指定时，优先选择 globalId
  4: optional string url
}

struct VexDeleteResponse {
  1: i32 code;
  2: string message;
}


service Vex {
  // 图像检索API，高频请求，设计为Thrift接口，不随便修改
  VexSearchResponse search(1: VexSearchRequest request);// 检索图片/实体
  VexAddResponse add(1: VexAddRequest request); // 添加图片/实体

  VexDeleteResponse deleteImage(1: i64 globalId)                    // 删除图片，不支持分库分表，保留老接口，兼容已有业务
  VexDeleteResponse deleteImage2(1: VexDeleteRequest request)       // 删除图片，支持分库分表，新接口
  
  VexDeleteResponse deleteEntity(1: VexDeleteEntityRequest request); // 删除实体

  VexImageResponse getImagesFeature(1: string imagesetName, 2: string tableName, 3: list<i64> globalIds); // 查询图像信息(包含特征)，globalIds长度最大为10

  // 查询实体对应的图像信息(可选是否包含特征), opt=true包含，false不包含
  // imagesetName 检索库名称，tableName 表名称
  VexEntityResponse getEntityImages(1: string imagesetName, 2: string tableName, 3: string entityName, 4: bool opt);
}