from octo_rpc import load
import os
import requests
from typing import Dict, List, Union
from utils.logger import logger

current_dir = os.path.dirname(os.path.abspath(__file__))    
vex = load(os.path.join(current_dir, "vex.thrift"))


## 根据小美喂喂的向量数据库配置，截取了向量化函数，其余部分暂时未保留，需要引入时去喂喂里找就行


def embedding(text: Union[str, List[str]]) -> List[float]|None:
    url = 'https://aigc.sankuai.com/v1/openai/native/embeddings'
    headers = {
        'Authorization': 'Bearer 1663502478995767378',
        'Content-Type': 'application/json'
    }
    data = {
        'model': 'text-embedding-miffy-002'
    }
    if isinstance(text, str):
        data['input'] = text
        try:
            response = requests.post(url, headers=headers, json=data)
            response.raise_for_status()  # 检查HTTP请求是否成功
            result = response.json()
            return result.get('data')[0].get('embedding')
        except requests.exceptions.RequestException as e:
            logger.error(f"请求embedding接口失败: {e}")
            return None
        except ValueError as e:
            logger.error(f"解析响应失败: {e}")
            return None
    elif isinstance(text, list):
        total_num = len(text)
        return_tensors = []
        for idx in range((total_num // 4) + 1):
            data['input'] = text[idx * 4:(idx + 1) * 4]
            if len(data['input']) == 0:
                break
            try:    
                response = requests.post(url, headers=headers, json=data)
                # logger.info(f"请求响应: {response.text}")
                response.raise_for_status()  # 检查HTTP请求是否成功
                result = response.json()
                return_tensors += [data.get('embedding') for data in result.get('data')]
            except Exception as e:
                logger.error(f"请求embedding接口失败: {e}")
                return None
        return return_tensors
    else:
        logger.error(f"Vex Embedding Parameters Error. Expected a string or a list of strings, but got {type(text)}")
        return {"status": "error",
                "message": f"Vex Embedding Parameters Error. Expected a string or a list of strings, but got {type(text)}"}


if __name__ == "__main__":
    # print(add("一条新数据1"))
    # print(search("一条新数据1", 3))


    # 测试向量化函数，如果你要测试，记得下载scikit-learn方便计算余弦相似度
    from sklearn.metrics.pairwise import cosine_similarity
    u_vec1 = [embedding("西红柿")]
    # u_vec2 = [embedding("用户喜欢吃粤菜")]
    # u_vec3 = [embedding("用户喜欢吃甜品")]

    s_vec1 = [embedding("番茄")]
    s_vec2 = [embedding("黄瓜")]
    s_vec3 = [embedding("柿子")]
    # s_vec3 = [embedding("这家店的大福很不错")]

    #测试推荐逻辑：对于用户甲，最推荐的应该是水煮肉片正宗的商家A
    # 对于用户乙，最不推荐的应该是白切鸡很难吃的商家B
    # 对于用户丙，最推荐的应该是大福好吃的商家C

    print(cosine_similarity(u_vec1, s_vec1),'u_vec1,s_vec1')
    print(cosine_similarity(u_vec1, s_vec2),'u_vec1,s_vec2')
    print(cosine_similarity(u_vec1, s_vec3),'u_vec1,s_vec3')

    # print(cosine_similarity(u_vec2, s_vec1),'u_vec2,s_vec1')
    # print(cosine_similarity(u_vec2, s_vec2),'u_vec2,s_vec2')
    # print(cosine_similarity(u_vec2, s_vec3),'u_vec2,s_vec3')

    # print(cosine_similarity(u_vec3, s_vec1),'u_vec3,s_vec1')
    # print(cosine_similarity(u_vec3, s_vec2),'u_vec3,s_vec2')
    # print(cosine_similarity(u_vec3, s_vec3),'u_vec3,s_vec3')

    