# xiaomei-humanrelation

## 增强记忆管理系统

基于"以人物为中心"的智能记忆管理系统，实现完整的记忆生命周期管理。人员信息作为长期记忆存储在MySQL中，事件信息作为短期记忆存储在ElasticSearch中。

### 核心架构

- **人物档案库 (Person Store)**: 使用MySQL存储，作为长期记忆，支持人物小传的动态更新
- **事件记忆库 (Event Store)**: 使用ElasticSearch存储，作为短期记忆，记录具体交互事件
- **智能记忆处理**: 实现时效性信息处理、记忆整合、定期更新等完整机制

### 核心功能特性

#### 1. 智能实体识别与时效性处理
- **身份识别与消歧**: 自动识别人物并避免重复创建
- **时效性信息计算**: 自动将年龄转换为出生年份等不变信息
- **验证式交互**: 对计算结果进行友好的确认提示

#### 2. 记忆生命周期管理
- **短期记忆**: 事件按时间顺序存储，记录具体交互
- **长期记忆**: 人物小传定期更新，整合相关事件信息
- **过期检查**: 定期检查需要更新的状态信息

#### 3. 智能对话增强
- **上下文检索**: 根据用户输入自动检索相关记忆
- **个性化回应**: 基于记忆信息提供针对性建议
- **记忆融入对话**: 在聊天中自然体现记忆效果

### 完整接口列表

#### 原有接口 (已增强)
1. **记忆搜索接口** - `POST /humanrelation/search_memory`
2. **流式聊天接口** - `POST /humanrelation/chat` (已集成记忆功能)
3. **添加记忆接口** - `POST /humanrelation/add_memory` (使用增强处理)
4. **读取记忆接口** - `POST /humanrelation/read_memory` (支持对话建议)

#### 人员管理接口
5. **查看所有人列表** - `GET /humanrelation/persons`
6. **查看某个人** - `GET /humanrelation/person/{person_id}`
7. **删除某个人** - `DELETE /humanrelation/person/{person_id}`
8. **增加某个人** - `POST /humanrelation/person`
9. **修改某个人的档案** - `PUT /humanrelation/person/{person_id}`
10. **搜索某个人** - `GET /humanrelation/search_person`

#### 新增记忆管理接口
11. **更新长期记忆** - `POST /humanrelation/update_long_memory`
    手动触发长期记忆更新，将短期记忆总结为人物小传

12. **检查过期信息** - `GET /humanrelation/check_outdated?days=180`
    检查需要更新的过期状态信息

#### 系统监控接口
13. **健康检查** - `GET /monitor/alive`
    用于部署时的存活性检查，返回服务状态

### Lion配置要求

您需要在Lion配置中心配置以下4个提示词：

#### 1. 记忆提取提示词 (humanrelation.memory_extraction_prompt)
```
你是一个记忆提取专家。请从用户的对话中提取人物信息和事件信息，返回JSON格式。

要求：
- 识别对话中提到的所有人物，提取姓名、别名、职业、关系等信息
- 识别具体的事件，包括时间、地点、参与者、主题、情感色彩
- 特别注意年龄等时效性信息，需要进行智能处理
- 返回格式：{"persons": [...], "events": [...]}

人物信息格式：
{
  "canonical_name": "正式姓名",
  "aliases": "别名或昵称",
  "profile_summary": "简短描述",
  "key_attributes": {
    "职业": "具体职位",
    "关系": "与用户的关系",
    "年龄": "如果提到年龄",
    "公司": "工作单位"
  }
}

事件信息格式：
{
  "description_text": "事件描述",
  "participants": ["参与者列表"],
  "location": "发生地点",
  "topics": ["相关主题"],
  "sentiment": "positive/negative/neutral"
}
```

#### 2. 记忆生成提示词 (humanrelation.memory_generation_prompt)
```
你是一个人际关系顾问。基于用户的查询和相关的人物档案、事件记忆，为用户提供有用的对话建议。

输入信息包括：
- persons: 相关人物档案列表
- events: 相关事件记忆列表  
- query_context: 用户当前的查询上下文

请提供：
1. 2-3个具体的对话话题建议
2. 需要关注的要点
3. 可以提及的共同经历或话题

回应要简洁实用，帮助用户进行更好的人际交流。
```

#### 3. 记忆整合提示词 (humanrelation.memory_integration_prompt)
```
你是一个记忆整合专家。判断新生成的人物信息是否需要与现有的人物档案进行整合。

输入：
- 新的人物信息
- 现有的相关人物档案

请判断是否需要整合，并返回整合后的信息。如果信息冲突，请智能合并。

返回JSON格式：
{
  "need_integration": true/false,
  "integrated_info": "整合后的信息",
  "action": "create_new/update_existing/merge"
}
```

#### 4. 长期记忆总结提示词 (humanrelation.long_memory_summarization_prompt)
```
你是一个记忆总结专家。基于一个人物的基本信息和相关事件，生成或更新该人物的小传。

输入：
- person: 人物基本信息
- events: 与该人物相关的事件列表

请生成一段连贯的人物小传，包括：
1. 基本信息（职业、关系等）
2. 重要经历和互动
3. 个人特点和偏好
4. 最近的动态

小传应该简洁但信息丰富，帮助用户快速了解这个人。
```

### 其他必需配置
- `humanrelation.event_index_name`: 事件索引名称（默认：memory_event_store）
- `humanrelation.memory_extraction_model`: 记忆提取模型（默认：gpt-4o-mini）

### 使用示例

#### 智能对话体验
```bash
# 1. 先添加一些记忆
curl -X POST "http://localhost:8080/humanrelation/add_memory" \
-H "Content-Type: application/json" \
-d '{"conversation_text": "昨天和张三在咖啡厅聊了新项目，他是产品经理，孩子5岁了", "user_id": "user123"}'

# 2. 进行对话，系统会自动检索相关记忆
curl -X POST "http://localhost:8080/humanrelation/chat" \
-H "Content-Type: application/json" \
-d '{"user_input": "我要和张三开会了，有什么建议吗？", "user_id": "user123"}'

# 3. 手动更新长期记忆
curl -X POST "http://localhost:8080/humanrelation/update_long_memory?user_id=user123"
```

### 架构优势

1. **时效性管理**: 自动处理可计算信息，如年龄转出生年份
2. **智能验证**: 对推算结果进行友好确认
3. **记忆融合**: 在对话中自然体现记忆效果
4. **生命周期**: 完整的短期到长期记忆转换机制
5. **过期检查**: 主动发现需要更新的信息
6. **中文友好**: 完整支持中文内容和交互