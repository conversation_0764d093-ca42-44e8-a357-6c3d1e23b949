#!/bin/bash

# Cursor Python 开发环境自动配置脚本
# 适用于 macOS

echo "🚀 开始配置 Cursor Python 开发环境..."

# 检测操作系统
if [[ "$OSTYPE" == "darwin"* ]]; then
    CURSOR_CONFIG_DIR="$HOME/Library/Application Support/Cursor/User"
    echo "✅ 检测到 macOS 系统"
elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
    CURSOR_CONFIG_DIR="$HOME/.config/Cursor/User"
    echo "✅ 检测到 Linux 系统"
else
    echo "❌ 不支持的操作系统: $OSTYPE"
    exit 1
fi

# 创建配置目录
mkdir -p "$CURSOR_CONFIG_DIR"

# 备份现有配置
if [ -f "$CURSOR_CONFIG_DIR/settings.json" ]; then
    cp "$CURSOR_CONFIG_DIR/settings.json" "$CURSOR_CONFIG_DIR/settings.json.backup.$(date +%Y%m%d_%H%M%S)"
    echo "📋 已备份现有配置"
fi

# 创建优化的 Cursor 设置
cat > "$CURSOR_CONFIG_DIR/settings.json" << 'EOF'
{
    // 字体和显示设置
    "editor.fontSize": 14,
    "editor.fontFamily": "'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace",
    "editor.lineHeight": 1.6,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.renderWhitespace": "boundary",
    "editor.rulers": [88, 120],
    "editor.wordWrap": "on",

    // 代码格式化和保存
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit",
        "source.fixAll": "explicit"
    },

    // Python 特定设置
    "python.formatting.provider": "none",
    "[python]": {
        "editor.defaultFormatter": "ms-python.black-formatter",
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.organizeImports": "explicit"
        }
    },
    "python.linting.enabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": true,
    "python.linting.mypyEnabled": true,
    "python.analysis.autoImportCompletions": true,
    "python.analysis.typeCheckingMode": "off",

    // Black 格式化工具配置
    "black-formatter.args": [
        "--line-length=120"
    ],

    // 放宽所有行长度限制
    "python.linting.flake8Args": [
        "--max-line-length=120"
    ],

    // 文件管理
    "files.autoSave": "afterDelay",
    "files.autoSaveDelay": 1000,
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "files.exclude": {
        "**/.git": true,
        "**/.svn": true,
        "**/.hg": true,
        "**/CVS": true,
        "**/.DS_Store": true,
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/node_modules": true,
        "**/.pytest_cache": true,
        "**/.mypy_cache": true
    },

    // 搜索设置
    "search.exclude": {
        "**/.git": true,
        "**/node_modules": true,
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/.pytest_cache": true,
        "**/.mypy_cache": true,
        "**/venv": true,
        "**/env": true
    },

    // 智能提示和导航
    "editor.suggestSelection": "first",
    "editor.acceptSuggestionOnCommitCharacter": false,
    "editor.quickSuggestions": {
        "other": "on",
        "comments": "off",
        "strings": "off"
    },
    "editor.parameterHints.enabled": true,
    "editor.hover.delay": 300,
    "editor.inlineSuggest.enabled": true,

    // Git 设置
    "git.enableSmartCommit": true,
    "git.confirmSync": false,
    "git.autofetch": true,

    // 终端设置
    "terminal.integrated.fontSize": 13,
    "terminal.integrated.fontFamily": "'SF Mono', 'Monaco', monospace",
    "terminal.integrated.inheritEnv": false,

    // 主题和外观
    "workbench.colorTheme": "GitHub Dark",
    "workbench.iconTheme": "material-icon-theme",
    "workbench.tree.indent": 20,

    // 自定义错误和警告颜色
    "workbench.colorCustomizations": {
        "editorError.foreground": "#ffa500",
        "editorWarning.foreground": "#ffcc00",
        "editorInfo.foreground": "#87ceeb",
        "editorError.border": "#ffa500",
        "editorWarning.border": "#ffcc00"
    },

    // 其他实用设置
    "explorer.confirmDelete": false,
    "explorer.confirmDragAndDrop": false,
    "breadcrumbs.enabled": true,
    "editor.minimap.enabled": true,
    "editor.minimap.showSlider": "always",
    "problems.decorations.enabled": true,

    // JSON 文件格式化
    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[jsonc]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },

    // Markdown 设置
    "[markdown]": {
        "editor.wordWrap": "on",
        "editor.quickSuggestions": {
            "comments": "off",
            "strings": "off",
            "other": "off"
        }
    }
}
EOF

echo "✅ Cursor 配置已更新"

# 创建项目级配置文件模板
echo "📁 创建项目配置文件模板..."

cat > .vscode/settings.json << 'EOF'
{
    // 针对 FastAPI 项目的设置
    "python.linting.flake8Args": [
        "--max-line-length=120",
        "--ignore=E203,W503,E501"
    ],

    // 确保 flake8 配置生效
    "python.linting.enabled": true,
    "python.linting.flake8Enabled": true,

    // 文件关联
    "files.associations": {
        "*.thrift": "thrift",
        "*.yml": "yaml",
        "*.yaml": "yaml"
    },

    // 项目特定的排除文件
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/.pytest_cache": true,
        "**/.mypy_cache": true,
        "**/data": false,
        "**/logs": true
    },

    // 搜索排除
    "search.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        "**/data": true,
        "**/logs": true
    }
}
EOF

cat > .flake8 << 'EOF'
[flake8]
max-line-length = 120
ignore = E203,W503,E501,E701,E702
exclude = __pycache__,venv,env,.git,.pytest_cache
EOF

cat > pyproject.toml << 'EOF'
[tool.black]
line-length = 120
target-version = ['py39']

[tool.isort]
profile = "black"
line_length = 120

[tool.flake8]
max-line-length = 120
ignore = ["E203", "W503", "E501", "E701", "E702"]
EOF

echo "✅ 项目配置文件已创建"

# 检查是否安装了必要的 Python 工具
echo "🔧 检查 Python 开发工具..."

if ! command -v black &> /dev/null; then
    echo "📦 安装 black..."
    pip install black
fi

if ! command -v isort &> /dev/null; then
    echo "📦 安装 isort..."
    pip install isort
fi

if ! command -v flake8 &> /dev/null; then
    echo "📦 安装 flake8..."
    pip install flake8
fi

if ! command -v autoflake &> /dev/null; then
    echo "📦 安装 autoflake..."
    pip install autoflake
fi

echo "✅ 所有工具已安装"

# 创建推荐扩展列表
cat > .vscode/extensions.json << 'EOF'
{
    "recommendations": [
        "ms-python.python",
        "ms-python.vscode-pylance",
        "ms-python.black-formatter",
        "ms-python.isort",
        "ms-python.flake8",
        "usernamehw.errorlens",
        "streetsidesoftware.code-spell-checker",
        "eamodio.gitlens",
        "esbenp.prettier-vscode",
        "zhuangtongfa.material-theme",
        "pkief.material-icon-theme",
        "Gruntfuggly.todo-tree"
    ]
}
EOF

echo "✅ 推荐扩展列表已创建"

echo ""
echo "🎉 配置完成！"
echo ""
echo "📋 接下来需要做的："
echo "1. 重启 Cursor"
echo "2. 安装推荐的扩展（编辑器会提示）"
echo "3. 享受优化的开发体验！"
echo ""
echo "💡 提示："
echo "- 保存 Python 文件时会自动格式化"
echo "- 错误提示颜色已改为橙色（更温和）"
echo "- 行长度限制设置为 120 字符"
echo ""
echo "🔄 如需恢复原配置，备份文件在："
echo "$CURSOR_CONFIG_DIR/settings.json.backup.*"
