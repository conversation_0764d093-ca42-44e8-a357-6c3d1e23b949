# 移除了金融数据库依赖 (tushare, yfinance, akshare, pytdx)
# 这些库导致 lxml 编译问题，且在人际关系管理系统中未使用
gevent
fastapi
uvicorn
websockets
openai
loguru
requests
cat
python-cat
octo-rpc==0.4.7
beautifulsoup4==4.12.2
opensearch-py
uvicorn
pydantic
zebraproxyclient==0.0.12
sqlalchemy==2.0.37
pymysql==1.1.1 #MySQL数据库连接驱动
cryptography==43.0.3 #MySQL连接加密支持
DBUtils==3.1.0 #数据库连接池工具
# langchain & langgraph 全家桶
langgraph
langchain
langchain_openai
langchain_community
langchain_core
langchain_anthropic
langchain-google-community
langchain-tavily


# 美团内部依赖，S3plus
kms-client-sdk
mssapi-mt

# 代码质量工具
black
isort
flake8
autoflake

